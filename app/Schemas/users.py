from pydantic import BaseModel, field_validator, computed_field
from uuid import UUID
from typing import Union, Optional, Dict, Any
from Models.users import UserTypeEnum
from config.config import settings
from services.file_storage import file_storage

class UserBase(BaseModel):
    username: str
    email: str
    mobile: str
    user_type: Union[str, UserTypeEnum]  # Can be either string or enum
    country: str | None = None  # Optional field for country
    profile_picture: str | None = None  # Optional field for profile picture URL

    @field_validator('user_type', mode='before')
    @classmethod
    def validate_user_type(cls, v):
        if isinstance(v, UserTypeEnum):
            return v.value
        return v

    class Config:
        from_attributes = True

class UserCreate(UserBase):
    password: str
    cnic: str | None = None  # Optional field for CNIC
    passport: str | None = None  # Optional field for Passport

class UserOut(UserBase):
    id: UUID
    is_email_verified: bool = False
    is_mobile_verified: bool = False

    @computed_field
    @property
    def profile_picture_data(self) -> Optional[Dict[str, Any]]:
        """Get the profile picture as base64 encoded data"""
        if self.profile_picture:
            return file_storage.get_profile_picture_data(self.profile_picture)
        return None

    @computed_field
    @property
    def profile_picture_url(self) -> Optional[str]:
        """Get the full URL for the profile picture (for backward compatibility)"""
        if self.profile_picture:
            return f"{settings.STATIC_FILES_URL}/{self.profile_picture}"
        return None

    @computed_field
    @property
    def profile_picture_thumbnail_url(self) -> Optional[str]:
        """Get the full URL for the profile picture thumbnail (for backward compatibility)"""
        if self.profile_picture:
            thumbnail_path = self.profile_picture.replace(
                "profile_pictures/", "profile_pictures/thumbnails/"
            )
            return f"{settings.STATIC_FILES_URL}/{thumbnail_path}"
        return None

    class Config:
        from_attributes = True

class Signin(BaseModel):
    email: str
    password: str