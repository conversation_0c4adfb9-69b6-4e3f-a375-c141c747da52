from pydantic import BaseModel, Field
from uuid import UUID
from typing import Optional
from datetime import datetime
from Schemas.users import UserOut

class TeacherProfileBase(BaseModel):
    bio: Optional[str] = None
    experience_years: Optional[int] = None
    specialization: Optional[str] = None
    website: Optional[str] = None
    office_hours: Optional[str] = None
    rating: Optional[float] = Field(None, ge=0.0, le=5.0)

class TeacherProfileCreate(TeacherProfileBase):
    pass

class TeacherProfileUpdate(BaseModel):
    qualification: Optional[str] = Field(None, description="Teacher's qualification")
    subject_specialty: Optional[str] = Field(None, description="Teacher's subject specialty")
    experience_years: Optional[int] = Field(None, description="Years of teaching experience")
    bio: Optional[str] = Field(None, description="Teacher's biography")

class TeacherProfileOut(TeacherProfileBase):
    id: UUID = Field(..., description="Unique identifier for the teacher profile")
    user: Optional[UserOut] = Field(None, description="Teacher user information")
    rating: Optional[float] = Field(None, ge=0.0, le=5.0)
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
        use_enum_values = True

class TeacherProfileList(TeacherProfileBase):
    id: UUID = Field(..., description="Unique identifier for the teacher profile")
    user_id: UUID = Field(..., description="ID of the teacher user")
    user: Optional[UserOut] = Field(None, description="Teacher user information")
    
    class Config:
        from_attributes = True
        use_enum_values = True 