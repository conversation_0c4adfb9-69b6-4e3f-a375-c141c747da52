from pydantic import BaseModel, Field
from uuid import UUID
from typing import Sequence, Optional
from Schemas.users import UserOut
from Schemas.TeacherModule.TeacherProfile import TeacherProfileOut

class Classroom(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the classroom")
    name: str = Field(..., description="Name of the classroom")
    description: str | None = Field(None, description="Description of the classroom")
    teacher_id: UUID = Field(..., description="ID of the teacher managing the classroom")

class StudentClassroom(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the student classroom relationship")
    student_id: UUID = Field(..., description="ID of the student")
    classroom_id: UUID = Field(..., description="ID of the classroom")
class StudentClassRequest(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the student class request")
    student_id: UUID = Field(..., description="ID of the student making the request")
    classroom_id: UUID = Field(..., description="ID of the classroom for which the request is made")

class ClassroomCreate(BaseModel):
    name: str = Field(..., description="Name of the classroom")
    description: str | None = Field(None, description="Description of the classroom")
    teacher_id: UUID = Field(..., description="ID of the teacher managing the classroom")


class ClassroomUpdate(BaseModel):
    name: str | None = Field(None, description="Name of the classroom")
    description: str | None = Field(None, description="Description of the classroom")
    teacher_id: UUID | None = Field(None, description="ID of the teacher managing the classroom")

class StudentClassroomCreate(BaseModel):
    student_id: UUID = Field(..., description="ID of the student")
    classroom_id: UUID = Field(..., description="ID of the classroom")

class StudentClassroomUpdate(BaseModel):
    student_id: UUID | None = Field(None, description="ID of the student")
    classroom_id: UUID | None = Field(None, description="ID of the classroom")

class StudentClassRequestCreate(BaseModel):
    student_id: UUID = Field(..., description="ID of the student making the request")
    classroom_id: UUID = Field(..., description="ID of the classroom for which the request is made")

class StudentClassRequestUpdate(BaseModel):
    student_id: UUID | None = Field(None, description="ID of the student making the request")
    classroom_id: UUID | None = Field(None, description="ID of the classroom for which the request is made")

class StudentClassroomOut(StudentClassroom):
    student: UUID = Field(..., description="ID of the student")
    classroom: UUID = Field(..., description="ID of the classroom")
    
    class Config:
        from_attributes = True
        use_enum_values = True

class StudentClassRequestOut(StudentClassRequest):
    student_user: UserOut = Field(..., description="User info of the student making the request")
    classroom: UUID = Field(..., description="ID of the classroom for which the request is made")
    
    class Config:
        from_attributes = True
        use_enum_values = True

class ClassroomOut(Classroom):
    students: Sequence[UserOut] = Field(
        default_factory=list, description="List of students in the classroom"
    )
    class_requests: Sequence[StudentClassRequestOut] = Field(
        default_factory=list, description="List of student class requests for the classroom"
    )
    
    class Config:
        from_attributes = True
        use_enum_values = True

class ClassroomOutList(Classroom):
    student_count: int = Field(..., description="Number of students in the classroom")
    class_request_count: int = Field(..., description="Number of student class requests for the classroom")
    
    class Config:
        from_attributes = True
        use_enum_values = True

class ClassroomOutLite(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the classroom")
    name: str = Field(..., description="Name of the classroom")
    
    class Config:
        from_attributes = True
        use_enum_values = True

class ClassroomOutForStudent(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the classroom")
    name: str = Field(..., description="Name of the classroom")
    teacher_name: str = Field(..., description="Name of the teacher")
    teacher_id: UUID = Field(..., description="ID of the teacher")
    class Config:
        from_attributes = True
        use_enum_values = True

class ClassroomDetailedOut(Classroom):
    """Enhanced classroom response with detailed teacher and student information"""
    teacher: UserOut = Field(..., description="Complete teacher user information")
    teacher_profile: Optional[TeacherProfileOut] = Field(None, description="Teacher profile with bio, experience, etc.")
    students: Sequence[UserOut] = Field(
        default_factory=list, description="List of students with complete profile information"
    )
    student_count: int = Field(..., description="Total number of students in the classroom")

    class Config:
        from_attributes = True
        use_enum_values = True
