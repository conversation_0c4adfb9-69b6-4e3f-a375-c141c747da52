from pydantic import BaseModel, Field
from uuid import UUID
from typing import Optional
from datetime import datetime

class TeacherSubscriptionBase(BaseModel):
    plan_id: Optional[UUID] = Field(None, description="ID of the subscription plan")
    start_date: Optional[datetime] = Field(None, description="Subscription start date")
    end_date: Optional[datetime] = Field(None, description="Subscription end date")
    status: Optional[str] = Field("active", description="Subscription status")
    auto_renew: Optional[bool] = Field(True, description="Auto-renewal setting")
    payment_reference: Optional[str] = Field(None, description="Payment reference")

class TeacherSubscriptionCreate(TeacherSubscriptionBase):
    teacher_profile_id: UUID = Field(..., description="ID of the teacher profile")
    plan_id: UUID = Field(..., description="ID of the subscription plan")

class TeacherSubscriptionUpdate(BaseModel):
    plan_id: Optional[UUID] = Field(None, description="ID of the subscription plan")
    end_date: Optional[datetime] = Field(None, description="Subscription end date")
    status: Optional[str] = Field(None, description="Subscription status")
    auto_renew: Optional[bool] = Field(None, description="Auto-renewal setting")
    payment_reference: Optional[str] = Field(None, description="Payment reference")

class TeacherSubscriptionOut(TeacherSubscriptionBase):
    id: UUID = Field(..., description="Unique identifier for the subscription")
    teacher_profile_id: UUID = Field(..., description="ID of the teacher profile")
    
    class Config:
        from_attributes = True
        use_enum_values = True

class TeacherSubscriptionList(TeacherSubscriptionBase):
    id: UUID = Field(..., description="Unique identifier for the subscription")
    teacher_profile_id: UUID = Field(..., description="ID of the teacher profile")
    
    class Config:
        from_attributes = True
        use_enum_values = True 