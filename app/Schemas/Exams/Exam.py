from pydantic import BaseModel, <PERSON>
from typing import Optional, List
from uuid import UUID
from datetime import datetime, timedelta
from Schemas.Exams.Questions import QuestionOut, QuestionCreate, QuestionStudentOut

class ExamBase(BaseModel):
    title: str
    description: Optional[str] = None
    total_marks: int = 0
    total_duration: int = 0  # in minutes
    start_time: Optional[datetime] = None

class ExamCreate(ExamBase):
    question_ids: List[UUID] = Field(default_factory=list)

class ExamUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    total_marks: Optional[int] = None
    total_duration: Optional[int] = None
    question_ids: Optional[List[UUID]] = None
    start_time: Optional[datetime] = None

class ExamUpdateComprehensive(BaseModel):
    """Restricted exam update schema for teachers - only duration, start time, and assignments"""
    total_duration: Optional[int] = Field(None, ge=1, description="Total duration in minutes")
    start_time: Optional[datetime] = Field(None, description="Exam start time")

    # Assignment management - dynamic add/remove students or classrooms
    assignment: Optional["ExamAssignmentUpdate"] = Field(None, description="Update exam assignments")

    class Config:
        json_schema_extra = {
            "example": {
                "total_duration": 120,
                "start_time": "2024-01-15T10:00:00",
                "assignment": {
                    "student_ids": ["123e4567-e89b-12d3-a456-************"],
                    "classroom_id": "123e4567-e89b-12d3-a456-************",
                    "action": "add"
                }
            }
        }

class ExamAssignmentUpdate(BaseModel):
    """Schema for updating exam assignments"""
    student_ids: Optional[List[UUID]] = Field(None, description="List of student IDs")
    classroom_id: Optional[UUID] = Field(None, description="Classroom ID to assign exam to all students")
    action: str = Field("replace", description="Action to perform: 'add', 'remove', or 'replace'")
    assignment_type: Optional[str] = Field(None, description="Type of assignment: 'individual', 'classroom', or 'mixed'")

    class Config:
        json_schema_extra = {
            "example": {
                "student_ids": ["123e4567-e89b-12d3-a456-************"],
                "classroom_id": "123e4567-e89b-12d3-a456-************",
                "action": "replace",
                "assignment_type": "classroom"
            }
        }

class ExamOut(ExamBase):
    id: UUID
    questions: List[QuestionOut] = []
    end_time: Optional[datetime] = None
    class Config:
        from_attributes = True

    @staticmethod
    def from_orm_with_end_time(obj):
        data = ExamOut.model_validate(obj)
        if obj.start_time and obj.total_duration:
            data.end_time = obj.start_time + timedelta(minutes=obj.total_duration)
        else:
            data.end_time = None
        return data

# New schemas for assignment
class ExamAssignmentIn(BaseModel):
    student_ids: Optional[List[UUID]] = None
    classroom_id: Optional[UUID] = None

class ExamCreateWithAssignment(ExamBase):
    questions: List[QuestionCreate] = Field(default_factory=list)
    assignment: ExamAssignmentIn

class ExamAssignmentOut(BaseModel):
    exam_id: UUID
    assigned_student_ids: List[UUID]

class ExamStudentOut(BaseModel):
    id: UUID
    title: str  # Added for exam submission compatibility
    description: Optional[str] = None  # Added for exam submission compatibility
    total_marks: int = 0  # Added for exam submission compatibility
    total_duration: int = 0  # Added for exam submission compatibility
    questions: List[QuestionStudentOut] = []
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    class Config:
        from_attributes = True

class ExamStudentMinimalOut(BaseModel):
    """
    Minimal exam information for students with status tracking.

    Status values:
    - assigned: Exam is assigned but not yet started
    - upcoming: Exam is scheduled for the future
    - ongoing: Exam is currently active (within start/end time)
    - started: Student has begun the exam but not completed
    - submitted: Student has completed and submitted the exam
    - disqualified: Student was disqualified during the exam
    - ended: Exam time has passed without submission
    """
    id: UUID
    title: str
    description: Optional[str] = None
    total_marks: int = 0
    total_duration: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: str = "assigned"
    class Config:
        from_attributes = True

class ExamListOut(BaseModel):
    """Minimal exam information for listing purposes - no questions included"""
    id: UUID
    title: str
    description: Optional[str] = None
    total_marks: int = 0
    total_duration: int = 0  # in minutes
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    total_questions: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

    @staticmethod
    def from_orm_with_calculated_fields(obj):
        """Create ExamListOut with calculated fields like end_time and total_questions"""
        data = ExamListOut.model_validate(obj)

        # Calculate end_time
        if obj.start_time and obj.total_duration:
            data.end_time = obj.start_time + timedelta(minutes=obj.total_duration)
        else:
            data.end_time = None

        # Calculate total_questions
        data.total_questions = len(obj.questions) if hasattr(obj, 'questions') and obj.questions else 0

        return data

class ExamDetailedOut(BaseModel):
    """Comprehensive exam output with all details for teachers"""
    id: UUID
    title: str
    description: Optional[str] = None
    total_marks: int = 0
    total_duration: int = 0  # in minutes
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    questions: List[QuestionOut] = []
    assigned_students: List["StudentAssignmentOut"] = []
    assignment_info: Optional["ExamAssignmentInfo"] = None
    class_number: Optional[str] = None  # Extracted from questions
    total_questions: int = 0
    total_assigned_students: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

    @staticmethod
    def from_orm_with_assignments(obj, assigned_students=None, assignment_info=None):
        """Create ExamDetailedOut with assignment information"""
        data = ExamDetailedOut.model_validate(obj)

        # Calculate end_time
        if obj.start_time and obj.total_duration:
            data.end_time = obj.start_time + timedelta(minutes=obj.total_duration)
        else:
            data.end_time = None

        # Extract class number from questions (they should all have the same class)
        class_number = None
        if hasattr(obj, 'questions') and obj.questions:
            for question in obj.questions:
                if hasattr(question, 'class_') and question.class_ and hasattr(question.class_, 'ClassNo'):
                    class_number = question.class_.ClassNo
                    break  # All questions should have the same class, so we can break after finding the first one

        # Calculate totals
        data.total_questions = len(obj.questions) if hasattr(obj, 'questions') and obj.questions else 0
        data.assigned_students = assigned_students or []
        data.assignment_info = assignment_info
        data.class_number = class_number
        data.total_assigned_students = len(data.assigned_students)

        return data

class StudentAssignmentOut(BaseModel):
    """Student assignment information for exam output"""
    student_id: UUID
    student_name: str
    student_email: str
    assigned_at: Optional[datetime] = None
    status: str = "assigned"  # assigned, started, completed

    class Config:
        from_attributes = True

class ExamAssignmentInfo(BaseModel):
    """Information about how the exam was assigned"""
    assignment_type: str  # "individual", "classroom", "mixed"
    assigned_classrooms: List["ClassroomAssignmentInfo"] = []
    individual_students_count: int = 0
    classroom_students_count: int = 0

    class Config:
        json_schema_extra = {
            "example": {
                "assignment_type": "classroom",
                "assigned_classrooms": [
                    {
                        "classroom_id": "123e4567-e89b-12d3-a456-************",
                        "classroom_name": "Grade 10A Mathematics",
                        "students_count": 25
                    }
                ],
                "individual_students_count": 0,
                "classroom_students_count": 25
            }
        }

class ClassroomAssignmentInfo(BaseModel):
    """Information about classroom assignments"""
    classroom_id: UUID
    classroom_name: str
    students_count: int

    class Config:
        from_attributes = True

# Update forward references
ExamUpdateComprehensive.model_rebuild()
ExamDetailedOut.model_rebuild()
ExamAssignmentInfo.model_rebuild()