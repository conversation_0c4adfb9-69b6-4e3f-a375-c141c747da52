import uuid
from datetime import datetime

from sqlalchemy import (
    Column, DateTime
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import declarative_base
from config.session import Base  # <-- Import Base from session.py


class BaseModel(Base):
    __abstract__ = True
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)
