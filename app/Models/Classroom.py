import enum

from sqlalchemy import (
    Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Foreign<PERSON>ey
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from .users import User

class RequestStatusEnum(enum.Enum):
    pending = "pending"
    accepted = "accepted"
    rejected = "rejected"

class Classroom(BaseModel):
    __tablename__ = "classrooms"

    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    teacher_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    # Relationships
    teacher = relationship("User", back_populates="classrooms")
    students = relationship("StudentClassroom", back_populates="classroom", cascade="all, delete-orphan")
    # inside Classroom class
    tasks = relationship('TaskClassroom', back_populates='classroom', cascade='all, delete-orphan')
    task_students = relationship('TaskClassroomStudent', back_populates='classroom', cascade='all, delete-orphan')
    announcements = relationship("Announcement", back_populates="classroom", cascade="all, delete-orphan")

class StudentClassroom(BaseModel):
    __tablename__ = "student_classrooms"

    student_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    classroom_id = Column(UUID(as_uuid=True), ForeignKey("classrooms.id"), nullable=False)

    # Relationships
    student = relationship("User", back_populates="student_classrooms")
    classroom = relationship("Classroom", back_populates="students")

class StudentClassRequest(BaseModel):
    __tablename__ = "student_class_requests"

    student_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    classroom_id = Column(UUID(as_uuid=True), ForeignKey("classrooms.id"), nullable=False)
    status = Column(Enum(RequestStatusEnum), default=RequestStatusEnum.pending, nullable=False)

    # Relationships
    student = relationship("User", back_populates="class_requests")
    classroom = relationship("Classroom", back_populates="class_requests")
Classroom.class_requests = relationship("StudentClassRequest", back_populates="classroom", cascade="all, delete-orphan")
User.classrooms = relationship("Classroom", back_populates="teacher", cascade="all, delete-orphan")
User.student_classrooms = relationship("StudentClassroom", back_populates="student", cascade="all, delete-orphan")
User.class_requests = relationship("StudentClassRequest", back_populates="student", cascade="all, delete-orphan")
# User.student_classrooms = relationship("StudentClassroom", back_populates="student", cascade="all, delete-orphan")
