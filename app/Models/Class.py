import enum

from sqlalchemy import (
    <PERSON>umn, String, <PERSON><PERSON>, <PERSON><PERSON>, ForeignKey
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel


class ClassNumber(BaseModel):
    __tablename__ = "Class"
    ClassNo = Column(String, nullable=False)
    questions = relationship('Question', back_populates='class_')
    