import datetime
import enum
from sqlalchemy import Column, DateTime, Integer, String, Boolean, Enum, ForeignKey, Text, LargeBinary, Float, CheckConstraint, Numeric, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
import uuid

# --- Enum for User Type ---
class UserTypeEnum(enum.Enum):
    admin = "admin"
    student = "student"
    teacher = "teacher"
    sponsor = "sponsor"
    institute = "institute"

# --- User Table ---
class User(BaseModel):
    __tablename__ = "users"

    username = Column(String, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    mobile = Column(String, unique=True, nullable=False)
    password_hash = Column(String, nullable=False)
    country = Column(String, nullable=True)
    profile_picture = Column(String, nullable=True)

    user_type = Column(Enum(UserTypeEnum), nullable=False)
    is_email_verified = Column(Boolean, default=False)
    is_mobile_verified = Column(Boolean, default=False)

    # Relationships
    sponsor_profile = relationship("SponsorProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    institute_profile = relationship("InstituteProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    cnic = relationship("CNIC", back_populates="user", uselist=False, cascade="all, delete-orphan")
    passport = relationship("Passport", back_populates="user", uselist=False, cascade="all, delete-orphan")
    # inside User class
    tasks = relationship('TaskStudents', back_populates='student', cascade='all, delete-orphan')
    task_classrooms = relationship('TaskClassroomStudent', back_populates='student', cascade='all, delete-orphan')
    task_attachments = relationship('TaskAttachment', back_populates='student', cascade='all, delete-orphan')
    student_task_attachments = relationship('StudentTaskAttachment', back_populates='student', cascade='all, delete-orphan')
    teacher_profile = relationship("TeacherProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")

# --- CNIC Table ---
class CNIC(BaseModel):
    __tablename__ = "cnic"

    encrypted_cnic = Column(String, nullable=False)
    cnic_hash = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="cnic")


# --- Passport Table ---
class Passport(BaseModel):
    __tablename__ = "passport"

    encrypted_passport = Column(String, nullable=False)
    passport_hash = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="passport")


# --- Subject Table ---
class Subject(BaseModel):
    __tablename__ = "subjects"

    id = Column(UUID(as_uuid=True), primary_key=True)
    name = Column(String, nullable=False, unique=True)

    tasks = relationship("Task", back_populates="subject", cascade="all, delete-orphan")
    chapters = relationship("Chapter", back_populates="subject", cascade="all, delete-orphan")
    questions = relationship('Question', back_populates='subject')


# --- Sponsor Profile ---
class SponsorProfile(BaseModel):
    __tablename__ = "sponsor_profile"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    company_name = Column(String, nullable=True)
    logo_url = Column(String, nullable=True)
    description = Column(String, nullable=True)

    user = relationship("User", back_populates="sponsor_profile")


# --- Institute Profile ---
class InstituteProfile(BaseModel):
    __tablename__ = "institute_profile"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    institute_name = Column(String, nullable=True)
    address = Column(String, nullable=True)
    accreditation = Column(String, nullable=True)

    user = relationship("User", back_populates="institute_profile")

class TeacherProfile(BaseModel):
    __tablename__ = 'teacher_profiles'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    teacher_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), unique=True, nullable=False)
    bio = Column(Text, nullable=True)
    experience_years = Column(Integer, nullable=True)
    specialization = Column(String, nullable=True)
    website = Column(String, nullable=True)
    office_hours = Column(String, nullable=True)
    rating = Column(Numeric(2, 1), CheckConstraint('rating >= 0 AND rating <= 5'), nullable=True, default=0.0)

    user = relationship("User", back_populates="teacher_profile")

    # 1-to-1 or 1-to-many if you want subscription history
    subscription = relationship("TeacherSubscription", back_populates="teacher_profile", uselist=False)


class Plan(BaseModel):
    __tablename__ = 'plans'

    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Integer, nullable=False)  # Price in cents
    duration_days = Column(Integer, nullable=False)
    features = Column(Text)  # JSON string or comma-separated features
    is_active = Column(Boolean, default=True)

    subscriptions = relationship("TeacherSubscription", back_populates="plan")


class TeacherSubscription(BaseModel):
    __tablename__ = 'teacher_subscriptions'

    teacher_profile_id = Column(UUID(as_uuid=True), ForeignKey('teacher_profiles.id'), nullable=False, unique=True)
    plan_id = Column(UUID(as_uuid=True), ForeignKey('plans.id'), nullable=True)  

    start_date = Column(DateTime, default=datetime.datetime.now(datetime.UTC))
    end_date = Column(DateTime)
    status = Column(String(50), default='active')  # active, expired, cancelled
    auto_renew = Column(Boolean, default=True)
    payment_reference = Column(String(255))  # e.g. Stripe ID

    teacher_profile = relationship("TeacherProfile", back_populates="subscription")
    plan = relationship("Plan", back_populates="subscriptions")

class TeacherRating(BaseModel):
    __tablename__ = 'teacher_ratings'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    teacher_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    rating = Column(Numeric(2, 1), CheckConstraint('rating >= 0 AND rating <= 5'), nullable=False)
    __table_args__ = (UniqueConstraint('teacher_id', 'student_id', name='unique_teacher_student_rating'),)

    # Optionally, add relationships if needed
    # teacher = relationship('User', foreign_keys=[teacher_id])
    # student = relationship('User', foreign_keys=[student_id])
