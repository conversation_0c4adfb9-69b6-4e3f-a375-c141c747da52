import enum

from sqlalchemy import (
    Column, String, <PERSON>olean, Enum, Foreign<PERSON>ey, Integer, Table, DateTime
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from datetime import datetime


class QuestionType(enum.Enum):
    MCQS = "MCQS"
    SHORT = "SHORT"
    LONG = "LONG"

class QuestionLevel(enum.Enum):
    EASY = "EASY"
    MEDIUM = "MEDIUM"
    HARD = "HARD"


class Question(BaseModel):
    __tablename__ = "questions"
    text = Column(String, nullable=False)
    answer = Column(String, nullable=False)
    Type = Column(Enum(QuestionType), nullable=False)
    Level = Column(Enum(QuestionLevel), nullable=False)
    imageUrl = Column(String, nullable=True)
    AI_created = Column(Boolean, default=False, nullable=False)
    marks = Column(Integer, nullable=False, default=1)  # New field for marks per question

    class_id = Column(UUID(as_uuid=True), ForeignKey('Class.id'), nullable=False)
    subject_id = Column(UUID(as_uuid=True), ForeignKey('subjects.id'), nullable=False)
    chapter_id = Column(UUID(as_uuid=True), ForeignKey('chapters.id'), nullable=False)
    topic_id = Column(UUID(as_uuid=True), ForeignKey('topics.id'), nullable=True)
    subtopic_id = Column(UUID(as_uuid=True), ForeignKey('subtopics.id'), nullable=True)
    teacher_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    teacher = relationship('User')

    class_ = relationship('ClassNumber', back_populates='questions')
    subject = relationship('Subject', back_populates='questions')
    chapter = relationship('Chapter', back_populates='questions')
    topic = relationship('Topic', back_populates='questions')
    subtopic = relationship('SubTopic', back_populates='questions')


class McqsQuestionsOptions(BaseModel):
    __tablename__ = "mcqs_questions_options"
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), nullable=False)
    option_text = Column(String, nullable=False)
    is_correct = Column(Boolean, default=False)

    question = relationship('Question', back_populates='options')

Question.options = relationship('McqsQuestionsOptions', back_populates='question', cascade="all, delete-orphan")

