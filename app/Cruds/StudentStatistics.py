import uuid
from typing import Dict, <PERSON>, <PERSON>tional, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc
from datetime import datetime, timedelta, timezone
from collections import defaultdict

# Import caching utilities
from utils.cache import (
    get_cached_student_statistics, cache_student_statistics,
    get_cached_student_ranking, cache_student_ranking,
    CacheConfig
)

from Models.Exam import Exam, StudentExamAttempt, StudentExamAnswer
from Models.Exam import StudentExamAIResult
from Models.Questions import Question
from Models.users import User
from Models.users import Subject
from Models.Chapter import Chapter
from Models.Class import ClassNumber
from Schemas.StudentStatistics import (
    StudentStatistics, SubjectPerformance, ChapterPerformance, 
    StudentRanking, SubjectRanking, PeerComparison, PerformanceTrend,
    QuickStats
)


async def get_student_comprehensive_statistics(
    db: Session,
    student_id: uuid.UUID,
    time_period_days: int = 90,
    include_peer_comparison: bool = True,
    include_trends: bool = True,
    redis_client = None
) -> StudentStatistics:
    """Get comprehensive statistics for a student - OPTIMIZED with caching"""

    # OPTIMIZATION: Check cache first
    if redis_client:
        cache_key = f"comprehensive_stats:{student_id}:{time_period_days}:{include_peer_comparison}:{include_trends}"
        cached_stats = await get_cached_student_statistics(cache_key, redis_client)
        if cached_stats:
            return StudentStatistics(**cached_stats)

    # Get student info
    student = db.query(User).filter(User.id == student_id).first()
    if not student:
        raise ValueError("Student not found")
    
    # Get student's class info
    class_info = _get_student_class_info(db, student_id)
    
    # Calculate time range
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=time_period_days)
    
    # Get all exam attempts for the student
    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None),
        StudentExamAttempt.completed_at >= start_date
    ).all()
    
    # Calculate overall performance
    total_exams = len(attempts)
    total_marks_obtained, total_marks_possible, overall_average = _calculate_overall_performance(db, attempts)
    
    # Get rankings
    ranking = _calculate_student_ranking(db, student_id, overall_average)
    subject_rankings = _calculate_subject_rankings(db, student_id, time_period_days)
    
    # Get subject performance
    subject_performance = _calculate_subject_performance(db, student_id, attempts)
    
    # Get chapter performance
    chapter_performance = _calculate_chapter_performance(db, student_id, attempts)
    
    # Get peer comparison
    peer_comparison = None
    if include_peer_comparison:
        peer_comparison = _calculate_peer_comparison(db, student_id, class_info, overall_average)
    
    # Get performance trends
    performance_trend = None
    if include_trends:
        performance_trend = _calculate_performance_trend(db, student_id, time_period_days)
    
    # Identify strengths and weaknesses
    strongest_subjects, weakest_subjects = _identify_subject_strengths_weaknesses(subject_performance)
    strongest_chapters, weakest_chapters = _identify_chapter_strengths_weaknesses(chapter_performance)
    
    # Get recent exam performance
    recent_exam_performance = _get_recent_exam_performance(db, student_id, limit=5)
    
    # Create the statistics object
    statistics = StudentStatistics(
        student_id=student_id,
        student_name=student.username,
        class_name=class_info.get('class_name') if class_info else None,
        total_exams_taken=total_exams,
        overall_average=overall_average,
        total_marks_obtained=total_marks_obtained,
        total_marks_possible=total_marks_possible,
        ranking=ranking,
        subject_rankings=subject_rankings,
        subject_performance=subject_performance,
        chapter_performance=chapter_performance,
        peer_comparison=peer_comparison,
        performance_trend=performance_trend,
        strongest_subjects=strongest_subjects,
        weakest_subjects=weakest_subjects,
        strongest_chapters=strongest_chapters,
        weakest_chapters=weakest_chapters,
        recent_exam_performance=recent_exam_performance,
        last_updated=datetime.now(timezone.utc)
    )

    # OPTIMIZATION: Cache the result for future requests
    if redis_client:
        cache_key = f"comprehensive_stats:{student_id}:{time_period_days}:{include_peer_comparison}:{include_trends}"
        await cache_student_statistics(cache_key, statistics.dict(), redis_client)

    return statistics


def get_quick_statistics(db: Session, student_id: uuid.UUID) -> QuickStats:
    """Get quick overview statistics for dashboard"""
    
    # Get total exams taken
    total_exams = db.query(func.count(StudentExamAttempt.id)).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).scalar() or 0
    
    # Get average score
    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).all()
    
    _, _, average_score = _calculate_overall_performance(db, attempts)
    
    # Get current rank
    ranking = _calculate_student_ranking(db, student_id, average_score)
    
    # Get improvement trend
    trend = _get_simple_trend(db, student_id)
    
    # Get last exam score
    last_attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).order_by(desc(StudentExamAttempt.completed_at)).first()
    
    last_exam_score = None
    if last_attempt:
        last_exam_score = _calculate_attempt_percentage(db, last_attempt)
    
    # Get next exam (if any)
    # This would require exam scheduling data - placeholder for now
    next_exam_date = None
    
    return QuickStats(
        total_exams=total_exams,
        average_score=average_score,
        current_rank=ranking.overall_rank,
        total_students=ranking.total_students,
        improvement_trend=trend,
        last_exam_score=last_exam_score,
        next_exam_date=next_exam_date
    )


def _get_student_class_info(db: Session, student_id: uuid.UUID) -> Optional[Dict]:
    """Get student's class information"""
    # This would depend on how student-class relationships are stored
    # Placeholder implementation
    return {"class_name": "Class 10", "class_id": None}


def _calculate_overall_performance(db: Session, attempts: List[StudentExamAttempt]) -> Tuple[int, int, float]:
    """Calculate overall performance metrics - OPTIMIZED to avoid N+1 queries"""
    if not attempts:
        return 0, 0, 0.0

    # OPTIMIZATION: Get all AI results in a single query instead of N+1
    attempt_ids = [attempt.id for attempt in attempts]

    # Single query to get all AI results for all attempts
    ai_results_query = db.query(
        StudentExamAIResult.attempt_id,
        func.sum(StudentExamAIResult.ai_score).label('total_score'),
        func.sum(Question.marks).label('total_possible')
    ).join(Question, StudentExamAIResult.question_id == Question.id)\
     .filter(StudentExamAIResult.attempt_id.in_(attempt_ids))\
     .group_by(StudentExamAIResult.attempt_id).all()

    # Create lookup dictionary for O(1) access
    results_lookup = {str(result.attempt_id): (result.total_score or 0, result.total_possible or 0)
                     for result in ai_results_query}

    total_obtained = 0
    total_possible = 0

    # Process results using lookup (no additional DB queries)
    for attempt in attempts:
        obtained, possible = results_lookup.get(str(attempt.id), (0, 0))
        total_obtained += obtained
        total_possible += possible

    average = (total_obtained / total_possible * 100) if total_possible > 0 else 0.0
    return total_obtained, total_possible, round(average, 2)


def _calculate_attempt_marks(db: Session, attempt: StudentExamAttempt) -> Tuple[int, int]:
    """Calculate marks for a single attempt - OPTIMIZED with single query"""
    # OPTIMIZATION: Single query with JOIN instead of N+1 queries for questions
    result = db.query(
        func.sum(StudentExamAIResult.ai_score).label('total_obtained'),
        func.sum(Question.marks).label('total_possible')
    ).join(Question, StudentExamAIResult.question_id == Question.id)\
     .filter(StudentExamAIResult.attempt_id == attempt.id).first()

    if result and result.total_obtained is not None:
        return int(result.total_obtained or 0), int(result.total_possible or 0)

    # Fallback: calculate from exam structure (optimized with eager loading)
    exam = db.query(Exam).options(
        joinedload(Exam.questions)
    ).filter(Exam.id == attempt.exam_id).first()

    if exam and exam.questions:
        possible = sum(q.marks or 1 for q in exam.questions)
        return 0, possible  # No AI results means 0 obtained

    return 0, 0


def _calculate_attempt_percentage(db: Session, attempt: StudentExamAttempt) -> float:
    """Calculate percentage for a single attempt"""
    obtained, possible = _calculate_attempt_marks(db, attempt)
    return (obtained / possible * 100) if possible > 0 else 0.0


def _calculate_student_ranking(db: Session, student_id: uuid.UUID, student_average: float) -> StudentRanking:
    """Calculate student's overall ranking - OPTIMIZED with database-level ranking"""

    # OPTIMIZATION: Use database window functions for ranking instead of Python loops
    # This single query calculates all student averages and rankings at database level
    ranking_query = """
    WITH student_averages AS (
        SELECT
            u.id as student_id,
            COALESCE(
                (SUM(air.ai_score) * 100.0 / NULLIF(SUM(q.marks), 0)),
                0
            ) as average_score
        FROM users u
        LEFT JOIN student_exam_attempts sea ON u.id = sea.student_id
            AND sea.completed_at IS NOT NULL
        LEFT JOIN student_exam_ai_results air ON sea.id = air.attempt_id
        LEFT JOIN questions q ON air.question_id = q.id
        WHERE u.user_type = 'student'
        GROUP BY u.id
        HAVING COUNT(sea.id) > 0  -- Only students with completed attempts
    ),
    ranked_students AS (
        SELECT
            student_id,
            average_score,
            ROW_NUMBER() OVER (ORDER BY average_score DESC) as rank,
            COUNT(*) OVER () as total_students
        FROM student_averages
    )
    SELECT rank, total_students
    FROM ranked_students
    WHERE student_id = :student_id
    """

    result = db.execute(ranking_query, {"student_id": str(student_id)}).fetchone()

    if result:
        rank, total_students = result
        percentile = ((total_students - rank + 1) / total_students * 100) if total_students > 0 else 0

        return StudentRanking(
            overall_rank=rank,
            total_students=total_students,
            percentile=round(percentile, 2),
            class_rank=None,  # Would need class-specific calculation
            total_class_students=None
        )
    else:
        # Fallback for students with no completed attempts
        total_students = db.query(User).filter(User.user_type == "student").count()
        return StudentRanking(
            overall_rank=total_students,  # Last rank
            total_students=total_students,
            percentile=0.0,
            class_rank=None,
            total_class_students=None
        )


def _calculate_subject_rankings(db: Session, student_id: uuid.UUID, time_period_days: int) -> List[SubjectRanking]:
    """Calculate rankings for each subject"""
    # This is a simplified implementation
    # In practice, you'd calculate subject-wise averages for all students
    return []


def _calculate_subject_performance(db: Session, student_id: uuid.UUID, attempts: List[StudentExamAttempt]) -> List[SubjectPerformance]:
    """Calculate performance for each subject"""
    subject_data = defaultdict(list)
    
    # Group attempts by subject
    for attempt in attempts:
        exam = db.query(Exam).filter(Exam.id == attempt.exam_id).first()
        if exam and exam.questions:
            # Get subject from first question (assuming all questions in exam are from same subject)
            first_question = exam.questions[0]
            subject = db.query(Subject).filter(Subject.id == first_question.subject_id).first()
            
            if subject:
                percentage = _calculate_attempt_percentage(db, attempt)
                subject_data[subject.id].append({
                    'subject_name': subject.name,
                    'percentage': percentage,
                    'date': attempt.completed_at
                })
    
    # Calculate metrics for each subject
    subject_performances = []
    for subject_id, data in subject_data.items():
        if data:
            percentages = [d['percentage'] for d in data]
            subject_performances.append(SubjectPerformance(
                subject_id=subject_id,
                subject_name=data[0]['subject_name'],
                total_exams=len(data),
                average_score=round(sum(percentages) / len(percentages), 2),
                highest_score=max(percentages),
                lowest_score=min(percentages),
                total_marks_obtained=0,  # Would need detailed calculation
                total_marks_possible=0,  # Would need detailed calculation
                improvement_trend="Stable",  # Would need trend analysis
                last_exam_date=max(d['date'] for d in data),
                rank_in_subject=None  # Would need peer comparison
            ))
    
    return subject_performances


def _calculate_chapter_performance(db: Session, student_id: uuid.UUID, attempts: List[StudentExamAttempt]) -> List[ChapterPerformance]:
    """Calculate performance for each chapter"""
    # Simplified implementation - would need detailed question-level analysis
    return []


def _calculate_peer_comparison(db: Session, student_id: uuid.UUID, class_info: Optional[Dict], student_average: float) -> PeerComparison:
    """Calculate peer comparison metrics"""
    # Get peer average (simplified - would need class-based filtering)
    all_students = db.query(User).filter(
        User.user_type == "student",
        User.id != student_id
    ).all()
    
    peer_averages = []
    for student in all_students:
        attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.student_id == student.id,
            StudentExamAttempt.completed_at.isnot(None)
        ).all()
        
        if attempts:
            _, _, avg = _calculate_overall_performance(db, attempts)
            peer_averages.append(avg)
    
    peer_average = sum(peer_averages) / len(peer_averages) if peer_averages else 0
    
    if student_average > peer_average:
        status = "Above Average"
        improvement_needed = 0
    elif student_average == peer_average:
        status = "Average"
        improvement_needed = 0
    else:
        status = "Below Average"
        improvement_needed = peer_average - student_average
    
    return PeerComparison(
        student_average=student_average,
        peer_average=round(peer_average, 2),
        performance_status=status,
        improvement_needed=round(improvement_needed, 2),
        subjects_above_average=[],  # Would need subject-wise analysis
        subjects_below_average=[]   # Would need subject-wise analysis
    )


def _calculate_performance_trend(db: Session, student_id: uuid.UUID, time_period_days: int) -> PerformanceTrend:
    """Calculate performance trend over time"""
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=time_period_days)
    
    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None),
        StudentExamAttempt.completed_at >= start_date
    ).order_by(StudentExamAttempt.completed_at).all()
    
    if len(attempts) < 2:
        return PerformanceTrend(
            period=f"Last {time_period_days} days",
            trend_direction="Stable",
            trend_percentage=0.0,
            exam_scores=[],
            best_performing_month=None,
            worst_performing_month=None
        )
    
    # Calculate trend
    first_half = attempts[:len(attempts)//2]
    second_half = attempts[len(attempts)//2:]
    
    first_avg = sum(_calculate_attempt_percentage(db, a) for a in first_half) / len(first_half)
    second_avg = sum(_calculate_attempt_percentage(db, a) for a in second_half) / len(second_half)
    
    trend_percentage = second_avg - first_avg
    
    if trend_percentage > 5:
        trend_direction = "Improving"
    elif trend_percentage < -5:
        trend_direction = "Declining"
    else:
        trend_direction = "Stable"
    
    # Prepare exam scores data
    exam_scores = []
    for attempt in attempts:
        percentage = _calculate_attempt_percentage(db, attempt)
        exam_scores.append({
            "date": attempt.completed_at.isoformat(),
            "score": percentage,
            "exam_id": str(attempt.exam_id)
        })
    
    return PerformanceTrend(
        period=f"Last {time_period_days} days",
        trend_direction=trend_direction,
        trend_percentage=round(trend_percentage, 2),
        exam_scores=exam_scores,
        best_performing_month=None,  # Would need monthly analysis
        worst_performing_month=None  # Would need monthly analysis
    )


def _identify_subject_strengths_weaknesses(subject_performances: List[SubjectPerformance]) -> Tuple[List[str], List[str]]:
    """Identify strongest and weakest subjects"""
    if not subject_performances:
        return [], []
    
    sorted_subjects = sorted(subject_performances, key=lambda x: x.average_score, reverse=True)
    
    strongest = [s.subject_name for s in sorted_subjects[:3]]  # Top 3
    weakest = [s.subject_name for s in sorted_subjects[-3:]]   # Bottom 3
    
    return strongest, weakest


def _identify_chapter_strengths_weaknesses(chapter_performances: List[ChapterPerformance]) -> Tuple[List[str], List[str]]:
    """Identify strongest and weakest chapters"""
    # Simplified implementation
    return [], []


def _get_recent_exam_performance(db: Session, student_id: uuid.UUID, limit: int = 5) -> List[Dict]:
    """Get recent exam performance data"""
    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).order_by(desc(StudentExamAttempt.completed_at)).limit(limit).all()
    
    recent_performance = []
    for attempt in attempts:
        exam = db.query(Exam).filter(Exam.id == attempt.exam_id).first()
        percentage = _calculate_attempt_percentage(db, attempt)
        
        recent_performance.append({
            "exam_id": str(attempt.exam_id),
            "exam_title": exam.title if exam else "Unknown Exam",
            "score_percentage": percentage,
            "completed_at": attempt.completed_at.isoformat(),
            "status": attempt.status or "completed"
        })
    
    return recent_performance


def _get_simple_trend(db: Session, student_id: uuid.UUID) -> str:
    """Get simple trend direction for quick stats"""
    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).order_by(desc(StudentExamAttempt.completed_at)).limit(5).all()
    
    if len(attempts) < 3:
        return "Stable"
    
    recent_scores = [_calculate_attempt_percentage(db, a) for a in attempts]
    
    # Simple trend: compare first and last
    if recent_scores[0] > recent_scores[-1] + 5:
        return "Improving"
    elif recent_scores[0] < recent_scores[-1] - 5:
        return "Declining"
    else:
        return "Stable"
