import uuid
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from Models.TeacherQuestion import TeacherQuestion
from Models.users import TeacherProfile
from Models.Questions import Question, McqsQuestionsOptions
from Schemas.TeacherModule.TeacherQuestion import TeacherQuestionCreate, TeacherQuestionOut

def create_teacher_question(db: Session, data: TeacherQuestionCreate) -> TeacherQuestionOut:
    # Check if teacher_profile exists
    teacher_profile = db.query(TeacherProfile).filter(TeacherProfile.id == data.teacher_profile_id).first()
    if not teacher_profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found.")
    # Create the Question
    question_in = data.question
    new_question = Question(
        text=question_in.text,
        answer=question_in.answer,
        Type=question_in.Type,
        Level=question_in.Level,
        imageUrl=question_in.imageUrl,
        class_id=question_in.class_id,
        subject_id=question_in.subject_id,
        chapter_id=question_in.chapter_id,
        topic_id=question_in.topic_id,
        subtopic_id=question_in.subtopic_id,
        marks=question_in.marks if hasattr(question_in, 'marks') else 1
    )
    db.add(new_question)
    db.flush()  # get new_question.id
    # Add MCQ options if provided
    if question_in.Type == 'MCQS' and question_in.options is not None:
        for opt in question_in.options:
            option = McqsQuestionsOptions(
                question_id=new_question.id,
                option_text=opt.option_text,
                is_correct=opt.is_correct
            )
            db.add(option)
    # Create the TeacherQuestion relation
    tq = TeacherQuestion(teacher_profile_id=data.teacher_profile_id, question_id=new_question.id)
    db.add(tq)
    db.commit()
    db.refresh(tq)
    return TeacherQuestionOut.model_validate(tq)

def get_all_teacher_questions(db: Session):
    return db.query(TeacherQuestion).all()

def get_teacher_question_by_id(db: Session, id: uuid.UUID):
    tq = db.query(TeacherQuestion).filter(TeacherQuestion.id == id).first()
    if not tq:
        raise HTTPException(status_code=404, detail="TeacherQuestion not found.")
    return tq

def delete_teacher_question(db: Session, id: uuid.UUID):
    tq = db.query(TeacherQuestion).filter(TeacherQuestion.id == id).first()
    if not tq:
        raise HTTPException(status_code=404, detail="TeacherQuestion not found.")
    db.delete(tq)
    db.commit()
    return None 