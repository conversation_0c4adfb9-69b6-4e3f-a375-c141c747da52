import uuid
from sqlalchemy.orm import Session
from fastapi import HTTPException
from Models.users import User, TeacherProfile, TeacherSubject, Subject
from Schemas.users import UserOut
from Schemas.TeacherModule.Teacher import TeacherCreate, TeacherUpdate, TeacherOut, TeacherSubjectOut
from Schemas.Token import Token

def create_teacher_profile(db: Session, teacher: TeacherCreate) -> TeacherOut:
    # Check if user exists and is a teacher
    user = db.query(User).filter(User.id == teacher.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    if user.user_type != "teacher":
        raise HTTPException(status_code=400, detail="User is not a teacher.")
    
    # Check if teacher profile already exists
    existing_profile = db.query(TeacherProfile).filter(TeacherProfile.user_id == teacher.user_id).first()
    if existing_profile:
        raise HTTPException(status_code=400, detail="Teacher profile already exists.")
    
    # Create teacher profile
    teacher_profile = TeacherProfile(
        user_id=teacher.user_id,
        bio=teacher.bio
    )
    db.add(teacher_profile)
    db.flush()  # Get the profile ID
    
    db.commit()
    db.refresh(teacher_profile)
    
    # Return teacher profile with subjects
    return get_teacher_profile(db, teacher.user_id)

def get_all_teachers(db: Session) -> list[TeacherOut]:
    teacher_profiles = db.query(TeacherProfile).all()
    return [get_teacher_profile(db, profile.user_id) for profile in teacher_profiles]

def get_teacher_by_id(db: Session, user_id: uuid.UUID) -> TeacherOut:
    return get_teacher_profile(db, user_id)

def delete_teacher(db: Session, user_id: uuid.UUID) -> TeacherOut:
    user = db.query(User).filter(User.id == user_id, User.user_type == "teacher").first()
    if not user:
        raise HTTPException(status_code=404, detail="Teacher not found.")
    
    teacher_profile = db.query(TeacherProfile).filter(TeacherProfile.user_id == user_id).first()
    if not teacher_profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found.")
    
    # Get profile data before deletion
    profile_data = get_teacher_profile(db, user_id)
    
    # Delete teacher profile (cascade will handle TeacherSubject relationships)
    db.delete(teacher_profile)
    db.commit()
    
    return profile_data

def update_teacher_profile(db: Session, user_id: uuid.UUID, teacher_update: TeacherUpdate) -> TeacherOut:
    user = db.query(User).filter(User.id == user_id, User.user_type == "teacher").first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    
    teacher_profile = db.query(TeacherProfile).filter(TeacherProfile.user_id == user_id).first()
    if not teacher_profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found.")
    
    # Update bio if provided
    if teacher_update.bio is not None:
        teacher_profile.bio = teacher_update.bio
    
    db.commit()
    db.refresh(teacher_profile)
    
    return get_teacher_profile(db, user_id)

def get_teacher_profile(db: Session, user_id: uuid.UUID) -> TeacherOut:
    teacher_profile = db.query(TeacherProfile).filter(TeacherProfile.user_id == user_id).first()
    if not teacher_profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found.")
    
    # Create TeacherOut object
    return TeacherOut(
        user_id=teacher_profile.user_id,
        bio=teacher_profile.bio
    )


