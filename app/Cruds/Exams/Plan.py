from typing import List, Optional
import uuid
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException
from Models.users import Plan, TeacherSubscription
from Schemas.Exams.Plan import (
    PlanCreate,
    PlanOut,
    PlanUpdate,
    PlanList,
    PlanWithSubscriptionCount,
)

def create_plan(db: Session, plan: PlanCreate) -> PlanOut:
    # Check if plan with same name already exists
    existing_plan = db.query(Plan).filter(Plan.name == plan.name).first()
    if existing_plan:
        raise HTTPException(
            status_code=400,
            detail="Plan with this name already exists."
        )

    db_plan = Plan(
        name=plan.name,
        description=plan.description,
        price=plan.price,
        duration_days=plan.duration_days,
        features=plan.features,
        is_active=plan.is_active
    )
    db.add(db_plan)
    db.commit()
    db.refresh(db_plan)
    
    return PlanOut.model_validate(db_plan)

def get_plan_by_id(db: Session, plan_id: uuid.UUID) -> PlanOut:
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    return PlanOut.model_validate(plan)

def get_plan_by_name(db: Session, name: str) -> PlanOut:
    plan = db.query(Plan).filter(Plan.name == name).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    return PlanOut.model_validate(plan)

def get_all_plans(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    active_only: bool = False,
    name_filter: Optional[str] = None
) -> List[PlanList]:
    query = db.query(Plan)
    
    if active_only:
        query = query.filter(Plan.is_active == True)
    
    if name_filter:
        query = query.filter(Plan.name.ilike(f"%{name_filter}%"))
    
    plans = query.offset(skip).limit(limit).all()
    return [PlanList.model_validate(plan) for plan in plans]

def get_active_plans(db: Session) -> List[PlanList]:
    plans = db.query(Plan).filter(Plan.is_active == True).all()
    return [PlanList.model_validate(plan) for plan in plans]

def get_plans_with_subscription_count(db: Session) -> List[PlanWithSubscriptionCount]:
    """Get all plans with count of active subscriptions"""
    plans_with_count = (
        db.query(
            Plan,
            func.count(TeacherSubscription.id).label('subscription_count')
        )
        .outerjoin(TeacherSubscription, TeacherSubscription.plan_id == Plan.id)
        .filter(TeacherSubscription.status == "active")
        .group_by(Plan.id)
        .all()
    )
    
    result = []
    for plan, count in plans_with_count:
        plan_data = PlanWithSubscriptionCount.model_validate(plan)
        plan_data.subscription_count = count
        result.append(plan_data)
    
    return result

def update_plan(db: Session, plan_id: uuid.UUID, plan_update: PlanUpdate) -> PlanOut:
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    # Check if name is being updated and if it conflicts with existing plan
    if plan_update.name is not None and plan_update.name != plan.name:
        existing_plan = db.query(Plan).filter(
            Plan.name == plan_update.name,
            Plan.id != plan_id
        ).first()
        if existing_plan:
            raise HTTPException(
                status_code=400,
                detail="Plan with this name already exists."
            )
    
    if plan_update.name is not None:
        plan.name = plan_update.name
    if plan_update.description is not None:
        plan.description = plan_update.description
    if plan_update.price is not None:
        plan.price = plan_update.price
    if plan_update.duration_days is not None:
        plan.duration_days = plan_update.duration_days
    if plan_update.features is not None:
        plan.features = plan_update.features
    if plan_update.is_active is not None:
        plan.is_active = plan_update.is_active
    
    db.commit()
    db.refresh(plan)
    return PlanOut.model_validate(plan)

def activate_plan(db: Session, plan_id: uuid.UUID) -> PlanOut:
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    plan.is_active = True
    db.commit()
    db.refresh(plan)
    return PlanOut.model_validate(plan)

def deactivate_plan(db: Session, plan_id: uuid.UUID) -> PlanOut:
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    # Check if there are active subscriptions for this plan
    active_subscriptions = db.query(TeacherSubscription).filter(
        TeacherSubscription.plan_id == plan_id,
        TeacherSubscription.status == "active"
    ).count()
    
    if active_subscriptions > 0:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot deactivate plan. There are {active_subscriptions} active subscriptions."
        )
    
    plan.is_active = False
    db.commit()
    db.refresh(plan)
    return PlanOut.model_validate(plan)

def delete_plan(db: Session, plan_id: uuid.UUID) -> None:
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Plan not found")
    
    # Check if there are any subscriptions for this plan
    subscriptions_count = db.query(TeacherSubscription).filter(
        TeacherSubscription.plan_id == plan_id
    ).count()
    
    if subscriptions_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot delete plan. There are {subscriptions_count} subscriptions associated with this plan."
        )
    
    db.delete(plan)
    db.commit()

def get_plan_statistics(db: Session) -> dict:
    """Get statistics about plans and subscriptions"""
    total_plans = db.query(Plan).count()
    active_plans = db.query(Plan).filter(Plan.is_active == True).count()
    total_subscriptions = db.query(TeacherSubscription).count()
    active_subscriptions = db.query(TeacherSubscription).filter(
        TeacherSubscription.status == "active"
    ).count()
    
    # Get most popular plan
    popular_plan = (
        db.query(
            Plan.name,
            func.count(TeacherSubscription.id).label('subscription_count')
        )
        .outerjoin(TeacherSubscription, TeacherSubscription.plan_id == Plan.id)
        .group_by(Plan.id, Plan.name)
        .order_by(func.count(TeacherSubscription.id).desc())
        .first()
    )
    
    return {
        "total_plans": total_plans,
        "active_plans": active_plans,
        "total_subscriptions": total_subscriptions,
        "active_subscriptions": active_subscriptions,
        "most_popular_plan": {
            "name": popular_plan.name if popular_plan else None,
            "subscription_count": popular_plan.subscription_count if popular_plan else 0
        }
    } 