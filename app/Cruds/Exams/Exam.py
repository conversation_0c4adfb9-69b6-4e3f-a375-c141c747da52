import uuid
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from fastapi import HTTPException
from Models.Exam import Exam, StudentExamAssignment
from Models.Questions import Question
from Models.Classroom import Classroom, StudentClassroom
from Models.users import User
from Schemas.Exams.Exam import ExamCreateWithAssignment, ExamAssignmentOut, ExamUpdate, ExamUpdateComprehensive, ExamDetailedOut, StudentAssignmentOut, ExamAssignmentInfo, ClassroomAssignmentInfo
from typing import List, Optional
from Schemas.Exams.Questions import QuestionCreate
from Cruds.Exams.Questions import create_question

def get_utc_now():
    """Get current time in UTC"""
    return datetime.now(timezone.utc)

def ensure_utc_timezone(dt):
    """
    Ensure datetime is in UTC timezone.

    Args:
        dt: datetime object (can be naive or timezone-aware)

    Returns:
        datetime object in UTC timezone

    Rules:
    - If dt is None: return None
    - If dt is naive (no timezone): assume it's already UTC and add UTC timezone
    - If dt has timezone: convert to UTC
    """
    if dt is None:
        return None

    if dt.tzinfo is None:
        # Naive datetime - assume it's UTC and add timezone info
        return dt.replace(tzinfo=timezone.utc)
    else:
        # Timezone-aware datetime - convert to UTC
        return dt.astimezone(timezone.utc)

def create_exam_with_assignment(db: Session, exam_in: ExamCreateWithAssignment, user) -> ExamAssignmentOut:
    # Create the exam
    exam = Exam(
        title=exam_in.title,
        description=exam_in.description,
        total_marks=exam_in.total_marks,
        total_duration=exam_in.total_duration,
        start_time=exam_in.start_time  # Ensure start_time is set
    )
    db.add(exam)
    db.flush()
    # Create and assign questions
    created_questions = []
    for question_in in exam_in.questions:
        question = create_question(db, question_in, user)
        created_questions.append(question)
    exam.questions = created_questions
    db.flush()
    # Assignment logic
    student_ids = set(exam_in.assignment.student_ids or [])
    if exam_in.assignment.classroom_id:
        classroom = db.query(Classroom).filter(Classroom.id == exam_in.assignment.classroom_id).first()
        if not classroom:
            raise HTTPException(status_code=404, detail="Classroom not found.")
        classroom_students = db.query(StudentClassroom).filter(StudentClassroom.classroom_id == classroom.id).all()
        for sc in classroom_students:
            sid = getattr(sc, 'student_id', None)
            if isinstance(sid, uuid.UUID):
                student_ids.add(sid)
    # Remove any None values and ensure all are UUIDs
    student_ids = {sid for sid in student_ids if isinstance(sid, uuid.UUID)}
    # Assign exam to students
    assigned_ids = []
    for student_id in student_ids:
        # Check if student exists
        student = db.query(User).filter(User.id == student_id).first()
        if not student:
            continue
        assignment = StudentExamAssignment(exam_id=exam.id, student_id=student_id)
        db.add(assignment)
        assigned_ids.append(student_id)
    db.commit()
    return ExamAssignmentOut(exam_id=exam.id, assigned_student_ids=assigned_ids)

def get_exam_by_id(db: Session, exam_id: uuid.UUID, teacher_id: Optional[uuid.UUID] = None) -> Exam:
    # OPTIMIZATION: Use eager loading to avoid N+1 queries for questions
    exam = db.query(Exam).options(
        joinedload(Exam.questions)
    ).filter(Exam.id == exam_id).first()

    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")

    if teacher_id:
        # OPTIMIZATION: Check teacher access more efficiently
        question = db.query(Question).filter(
            Question.teacher_id == teacher_id,
            Question.exams.any(id=exam_id)
        ).first()
        if not question:
            raise HTTPException(status_code=403, detail="You do not have access to this exam.")

    return exam

def get_exam_by_id_comprehensive(db: Session, exam_id: uuid.UUID, teacher_id: Optional[uuid.UUID] = None) -> ExamDetailedOut:
    """
    Get comprehensive exam details including assigned students, assignment info, and class number.
    """
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")
    if teacher_id:
        # Check if the exam contains at least one question created by the teacher
        question = db.query(Question).filter(
            Question.teacher_id == teacher_id,
            Question.exams.any(id=exam_id)
        ).first()
        if not question:
            raise HTTPException(status_code=403, detail="You do not have access to this exam.")

    # Get assignment information
    assignments = db.query(StudentExamAssignment).filter(
        StudentExamAssignment.exam_id == exam_id
    ).all()

    assigned_students = []
    classroom_assignments = {}
    individual_count = 0

    for assignment in assignments:
        student = db.query(User).filter(User.id == assignment.student_id).first()
        if student:
            assigned_students.append(StudentAssignmentOut(
                student_id=student.id,
                student_name=student.username,
                student_email=student.email,
                assigned_at=assignment.assigned_at,
                status=assignment.status
            ))

            # Track assignment sources
            if assignment.assignment_source == "classroom" and assignment.source_classroom_id:
                if assignment.source_classroom_id not in classroom_assignments:
                    classroom = db.query(Classroom).filter(Classroom.id == assignment.source_classroom_id).first()
                    if classroom:
                        classroom_assignments[assignment.source_classroom_id] = {
                            "classroom_id": classroom.id,
                            "classroom_name": classroom.name,
                            "students_count": 0
                        }
                classroom_assignments[assignment.source_classroom_id]["students_count"] += 1
            else:
                individual_count += 1

    # Create assignment info
    assignment_info = None
    if assignments:
        # Determine overall assignment type
        has_classroom = len(classroom_assignments) > 0
        has_individual = individual_count > 0

        if has_classroom and has_individual:
            overall_type = "mixed"
        elif has_classroom:
            overall_type = "classroom"
        else:
            overall_type = "individual"

        classroom_info_list = [
            ClassroomAssignmentInfo(**info) for info in classroom_assignments.values()
        ]

        assignment_info = ExamAssignmentInfo(
            assignment_type=overall_type,
            assigned_classrooms=classroom_info_list,
            individual_students_count=individual_count,
            classroom_students_count=sum(info["students_count"] for info in classroom_assignments.values())
        )

    return ExamDetailedOut.from_orm_with_assignments(exam, assigned_students, assignment_info)

def get_all_exams(db: Session):
    return db.query(Exam).all()

def update_exam(db: Session, exam_id: uuid.UUID, exam_update: ExamUpdate, teacher_id: Optional[uuid.UUID] = None) -> Exam:
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")
    if teacher_id:
        question = db.query(Question).filter(
            Question.teacher_id == teacher_id,
            Question.exams.any(id=exam_id)
        ).first()
        if not question:
            raise HTTPException(status_code=403, detail="You do not have permission to update this exam.")

    # Only allow updating start_time and total_duration
    if exam_update.start_time is not None:
        # Ensure start_time is stored in UTC
        start_time_utc = ensure_utc_timezone(exam_update.start_time)
        print(f"🌍 Converting start_time to UTC: {exam_update.start_time} -> {start_time_utc}")
        exam.start_time = start_time_utc
    if exam_update.total_duration is not None:
        exam.total_duration = exam_update.total_duration

    db.commit()
    db.refresh(exam)
    return exam


def delete_exam(db: Session, exam_id: uuid.UUID, teacher_id: Optional[uuid.UUID] = None):
    """
    Delete exam and ALL associated data including:
    - Student assignments
    - Student attempts
    - Student answers
    - AI results
    - Teacher results
    - Cheat incidents
    - Question orders
    - All related records
    """
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")

    if teacher_id:
        question = db.query(Question).filter(
            Question.teacher_id == teacher_id,
            Question.exams.any(id=exam_id)
        ).first()
        if not question:
            raise HTTPException(status_code=403, detail="You do not have permission to delete this exam.")

    try:
        # Import all related models
        from Models.Exam import (
            StudentExamAssignment, StudentExamAttempt, StudentExamAnswer,
            StudentExamAIResult, StudentExamTeacherResult, StudentExamCheatIncident,
            StudentExamQuestionOrder
        )

        print(f"🗑️ Deleting exam {exam_id} and all associated data...")

        # Delete in correct order to avoid foreign key violations
        # 1. Delete AI results first
        ai_results_count = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id.in_(
                db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
            )
        ).count()
        if ai_results_count > 0:
            db.query(StudentExamAIResult).filter(
                StudentExamAIResult.attempt_id.in_(
                    db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
                )
            ).delete(synchronize_session=False)
            print(f"   ✅ Deleted {ai_results_count} AI results")

        # 2. Delete teacher results
        teacher_results_count = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id.in_(
                db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
            )
        ).count()
        if teacher_results_count > 0:
            db.query(StudentExamTeacherResult).filter(
                StudentExamTeacherResult.attempt_id.in_(
                    db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
                )
            ).delete(synchronize_session=False)
            print(f"   ✅ Deleted {teacher_results_count} teacher results")

        # 3. Delete cheat incidents
        cheat_incidents_count = db.query(StudentExamCheatIncident).filter(
            StudentExamCheatIncident.attempt_id.in_(
                db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
            )
        ).count()
        if cheat_incidents_count > 0:
            db.query(StudentExamCheatIncident).filter(
                StudentExamCheatIncident.attempt_id.in_(
                    db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
                )
            ).delete(synchronize_session=False)
            print(f"   ✅ Deleted {cheat_incidents_count} cheat incidents")

        # 4. Delete question orders
        question_orders_count = db.query(StudentExamQuestionOrder).filter(
            StudentExamQuestionOrder.attempt_id.in_(
                db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
            )
        ).count()
        if question_orders_count > 0:
            db.query(StudentExamQuestionOrder).filter(
                StudentExamQuestionOrder.attempt_id.in_(
                    db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
                )
            ).delete(synchronize_session=False)
            print(f"   ✅ Deleted {question_orders_count} question orders")

        # 5. Delete student answers
        answers_count = db.query(StudentExamAnswer).filter(
            StudentExamAnswer.attempt_id.in_(
                db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
            )
        ).count()
        if answers_count > 0:
            db.query(StudentExamAnswer).filter(
                StudentExamAnswer.attempt_id.in_(
                    db.query(StudentExamAttempt.id).filter(StudentExamAttempt.exam_id == exam_id)
                )
            ).delete(synchronize_session=False)
            print(f"   ✅ Deleted {answers_count} student answers")

        # 6. Delete student attempts
        attempts_count = db.query(StudentExamAttempt).filter(StudentExamAttempt.exam_id == exam_id).count()
        if attempts_count > 0:
            db.query(StudentExamAttempt).filter(StudentExamAttempt.exam_id == exam_id).delete()
            print(f"   ✅ Deleted {attempts_count} student attempts")

        # 7. Delete student assignments
        assignments_count = db.query(StudentExamAssignment).filter(StudentExamAssignment.exam_id == exam_id).count()
        if assignments_count > 0:
            db.query(StudentExamAssignment).filter(StudentExamAssignment.exam_id == exam_id).delete()
            print(f"   ✅ Deleted {assignments_count} student assignments")

        # 8. Finally delete the exam itself
        db.delete(exam)
        db.commit()

        print(f"   🎉 Successfully deleted exam '{exam.title}' and all associated data!")
        return {"message": f"Exam '{exam.title}' and all associated data deleted successfully"}

    except Exception as e:
        db.rollback()
        print(f"   ❌ Error deleting exam: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting exam: {str(e)}")

def get_exams_by_teacher(db: Session, teacher_id: uuid.UUID) -> List[Exam]:
    """
    Get all exams created by a specific teacher.
    This is determined by finding exams that have questions created by the teacher.
    """
    # Get all questions created by the teacher
    teacher_questions = db.query(Question).filter(Question.teacher_id == teacher_id).all()
    teacher_question_ids = [q.id for q in teacher_questions]

    # Get all exams that have at least one question created by the teacher
    exams = db.query(Exam).join(Exam.questions).filter(Question.id.in_(teacher_question_ids)).distinct().all()

    return exams

def get_all_exams_minimal(db: Session, skip: int = 0, limit: int = 100) -> List[Exam]:
    """
    Get all exams with minimal information for listing purposes.
    Includes questions count but not the actual question objects.
    """
    return db.query(Exam).offset(skip).limit(limit).all()

def get_exams_by_teacher_minimal(db: Session, teacher_id: uuid.UUID, skip: int = 0, limit: int = 100) -> List[Exam]:
    """
    Get all exams created by a specific teacher with minimal information.
    Optimized query that doesn't load question objects but includes count.
    """
    # Get all questions created by the teacher
    teacher_questions = db.query(Question).filter(Question.teacher_id == teacher_id).all()
    teacher_question_ids = [q.id for q in teacher_questions]

    if not teacher_question_ids:
        return []

    # Get all exams that have at least one question created by the teacher
    # Use distinct to avoid duplicates if exam has multiple teacher questions
    exams = (
        db.query(Exam)
        .join(Exam.questions)
        .filter(Question.id.in_(teacher_question_ids))
        .distinct()
        .offset(skip)
        .limit(limit)
        .all()
    )

    return exams

def get_all_exams_for_student(db: Session, student_id: uuid.UUID):
    """
    Get ALL assigned exams for a student with status information.
    Returns upcoming, ongoing, and completed exams with proper status.
    """
    from Models.Exam import StudentExamAttempt

    # Get all assignments for this student with exam details
    assignments = db.query(StudentExamAssignment).filter(
        StudentExamAssignment.student_id == student_id
    ).all()

    all_exams = []
    current_time = get_utc_now()

    for assignment in assignments:
        exam = db.query(Exam).filter(Exam.id == assignment.exam_id).first()
        if exam is not None:
            # Check if student has attempted this exam
            attempt = db.query(StudentExamAttempt).filter(
                StudentExamAttempt.exam_id == exam.id,
                StudentExamAttempt.student_id == student_id
            ).first()

            # Determine exam status based on assignment, attempt, and timing
            status = determine_exam_status(exam, assignment, attempt, current_time)

            # Add status to exam object for response
            exam.status = status
            all_exams.append(exam)

    return all_exams

def determine_exam_status(exam, assignment, attempt, current_time):
    """
    Determine the status of an exam for a student based on various factors.
    """
    # Check if student was disqualified
    if attempt and hasattr(attempt, 'status') and attempt.status == "disqualified":
        return "disqualified"

    # If student has completed the exam
    if attempt and attempt.completed_at:
        return "submitted"

    # If student has started the exam but not completed
    if attempt and attempt.started_at and not attempt.completed_at:
        return "started"

    # If exam has start time, check timing
    if exam.start_time:
        exam_start_time = exam.start_time
        if exam_start_time.tzinfo is None:
            exam_start_time = exam_start_time.replace(tzinfo=timezone.utc)

        exam_end_time = exam_start_time + timedelta(minutes=exam.total_duration)

        if current_time < exam_start_time:
            return "upcoming"
        elif current_time >= exam_start_time and current_time <= exam_end_time:
            return "ongoing"
        elif current_time > exam_end_time:
            return "ended"

    # Default to assignment status
    return assignment.status if assignment.status else "assigned"

def get_exam_for_student(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID):
    # Check assignment
    assignment = db.query(StudentExamAssignment).filter(
        StudentExamAssignment.exam_id == exam_id,
        StudentExamAssignment.student_id == student_id
    ).first()
    if not assignment:
        raise HTTPException(status_code=403, detail="You are not assigned to this exam.")
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")
    return exam


def assign_exam_to_students_and_classrooms(
    db: Session,
    exam_id: uuid.UUID,
    student_ids: Optional[List[uuid.UUID]] = None,
    classroom_ids: Optional[List[uuid.UUID]] = None,
    teacher_id: Optional[uuid.UUID] = None,
    action: str = "add"
) -> ExamAssignmentOut:
    """
    Assign an existing exam to students and/or classrooms.

    Args:
        db: Database session
        exam_id: ID of the exam to assign
        student_ids: List of student IDs to assign
        classroom_ids: List of classroom IDs to assign
        teacher_id: ID of the teacher making the assignment (for permission check)
        action: "add", "remove", or "replace"

    Returns:
        ExamAssignmentOut with updated assignment information
    """
    # Verify exam exists
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")

    # Check teacher permission if provided
    if teacher_id:
        question = db.query(Question).filter(
            Question.teacher_id == teacher_id,
            Question.exams.any(id=exam_id)
        ).first()
        if not question:
            raise HTTPException(status_code=403, detail="You do not have permission to assign this exam.")

    # Collect all student IDs from individual students and classrooms
    all_student_ids = set(student_ids or [])

    # Add students from classrooms
    if classroom_ids:
        for classroom_id in classroom_ids:
            classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
            if not classroom:
                raise HTTPException(status_code=404, detail=f"Classroom {classroom_id} not found.")

            classroom_students = db.query(StudentClassroom).filter(
                StudentClassroom.classroom_id == classroom_id
            ).all()

            for sc in classroom_students:
                if isinstance(sc.student_id, uuid.UUID):
                    all_student_ids.add(sc.student_id)

    # Handle different actions
    if action == "replace":
        # Remove all existing assignments
        db.query(StudentExamAssignment).filter(
            StudentExamAssignment.exam_id == exam_id
        ).delete()
        db.flush()

    # Get current assignments for add/remove operations
    current_assignments = db.query(StudentExamAssignment).filter(
        StudentExamAssignment.exam_id == exam_id
    ).all()
    current_student_ids = {assignment.student_id for assignment in current_assignments}

    assigned_ids = []

    if action in ["add", "replace"]:
        # Add new assignments
        for student_id in all_student_ids:
            if action == "add" and student_id in current_student_ids:
                # Skip if already assigned
                assigned_ids.append(student_id)
                continue

            # Verify student exists
            student = db.query(User).filter(User.id == student_id).first()
            if not student:
                continue

            # Create assignment
            assignment = StudentExamAssignment(
                exam_id=exam_id,
                student_id=student_id,
                assigned_at=get_utc_now()
            )
            db.add(assignment)
            assigned_ids.append(student_id)

    elif action == "remove":
        # Remove specified assignments
        for student_id in all_student_ids:
            if student_id in current_student_ids:
                db.query(StudentExamAssignment).filter(
                    StudentExamAssignment.exam_id == exam_id,
                    StudentExamAssignment.student_id == student_id
                ).delete()

        # Get remaining assignments
        remaining_assignments = db.query(StudentExamAssignment).filter(
            StudentExamAssignment.exam_id == exam_id
        ).all()
        assigned_ids = [assignment.student_id for assignment in remaining_assignments]

    db.commit()
    return ExamAssignmentOut(exam_id=exam_id, assigned_student_ids=assigned_ids)


def get_exam_assignments(db: Session, exam_id: uuid.UUID, teacher_id: Optional[uuid.UUID] = None) -> ExamAssignmentOut:
    """
    Get current assignments for an exam.

    Args:
        db: Database session
        exam_id: ID of the exam
        teacher_id: ID of the teacher (for permission check)

    Returns:
        ExamAssignmentOut with current assignment information
    """
    # Verify exam exists
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found.")

    # Check teacher permission if provided
    if teacher_id:
        question = db.query(Question).filter(
            Question.teacher_id == teacher_id,
            Question.exams.any(id=exam_id)
        ).first()
        if not question:
            raise HTTPException(status_code=403, detail="You do not have permission to view this exam's assignments.")

    # Get current assignments
    assignments = db.query(StudentExamAssignment).filter(
        StudentExamAssignment.exam_id == exam_id
    ).all()

    assigned_student_ids = [assignment.student_id for assignment in assignments]

    return ExamAssignmentOut(exam_id=exam_id, assigned_student_ids=assigned_student_ids)


def unassign_exam_from_students(
    db: Session,
    exam_id: uuid.UUID,
    student_ids: List[uuid.UUID],
    teacher_id: Optional[uuid.UUID] = None
) -> ExamAssignmentOut:
    """
    Remove exam assignment from specific students.

    Args:
        db: Database session
        exam_id: ID of the exam
        student_ids: List of student IDs to unassign
        teacher_id: ID of the teacher (for permission check)

    Returns:
        ExamAssignmentOut with updated assignment information
    """
    return assign_exam_to_students_and_classrooms(
        db=db,
        exam_id=exam_id,
        student_ids=student_ids,
        teacher_id=teacher_id,
        action="remove"
    )