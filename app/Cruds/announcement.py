from sqlalchemy.orm import Session
from Models.Announcements import Announcement
from Schemas.Announcement import Announce<PERSON><PERSON><PERSON>, AnnouncementUpdate
from uuid import <PERSON>UID

from fastapi import HTTPException
from .TeacherModule.tasks import get_tasks_by_classroom

def get(db: Session, announcement_id: UUID) -> Announcement:
    """Get an announcement by ID."""
    announcement = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if not announcement:
        raise HTTPException(status_code=404, detail="Announcement not found")
    return announcement

def get_multi(db: Session, classroom_id: UUID, skip: int = 0, limit: int = 100):
    """Get multiple announcements and classroom tasks as announcements."""
    # Get announcements
    announcements = db.query(Announcement).filter(Announcement.classroom_id == classroom_id).offset(skip).limit(limit).all()

    # Get tasks assigned to this classroom
    task_list_out = get_tasks_by_classroom(db, classroom_id, skip, limit)
    tasks = task_list_out.tasks  # This is a list of TaskOut

    # Convert tasks to a pseudo-announcement format
    task_announcements = []
    for task in tasks:
        task_announcements.append({
            "id": str(task.id),
            "title": f"TASK: {task.name}",
            "description": task.description,
            "created_at": getattr(task, "created_at", None),
            "type": "task",
        })

    # Convert announcements to dicts
    announcement_dicts = [
        {
            "id": str(a.id),
            "title": getattr(a, "title", "Announcement"),
            "description": getattr(a, "content", ""),
            "created_at": getattr(a, "created_at", None),
            "type": "announcement",
        }
        for a in announcements
    ]

    # Combine and sort by created_at (descending)
    combined = announcement_dicts + task_announcements
    combined.sort(key=lambda x: x["created_at"] or 0, reverse=True)

    return combined

def create(db: Session, obj_in: AnnouncementCreate, classroom_id: UUID) -> Announcement:
    """Create a new announcement."""
    try:
        # Create the announcement data without classroom_id since it's passed separately
        announcement_data = obj_in.model_dump()
        print(f"Creating announcement with data: {announcement_data}")
        print(f"Classroom ID: {classroom_id}")
        
        db_obj = Announcement(**announcement_data, classroom_id=classroom_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    except Exception as e:
        print(f"Error creating announcement: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create announcement: {str(e)}")

def update(db: Session, db_obj: Announcement, obj_in: AnnouncementUpdate) -> Announcement:
    """Update an existing announcement."""
    for field, value in obj_in.model_dump(exclude_unset=True).items():
        setattr(db_obj, field, value)
    db.commit()
    db.refresh(db_obj)
    return db_obj

def remove(db: Session, announcement_id: UUID) -> Announcement:
    """Delete an announcement by ID."""
    obj = db.query(Announcement).filter(Announcement.id == announcement_id).first()
    if not obj:
        raise HTTPException(status_code=404, detail="Announcement not found")
    db.delete(obj)
    db.commit()
    return obj 


