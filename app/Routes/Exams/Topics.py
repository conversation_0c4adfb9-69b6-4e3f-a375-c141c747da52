from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from Schemas.Token import Token
from config.deps import get_current_user
from config.permission import require_type
from config.security import oauth2_scheme
from Schemas.TeacherModule.tasks import (
    TopicCreate, TopicOut, TopicUpdate, TopicListOut, TopicWithSubTopicsOut
)
from Cruds.TeacherModule import tasks as crud
from config.session import get_db

router = APIRouter()

# ===== TOPIC ROUTES =====
@router.post("/", response_model=TopicOut)
def create_topic(topic: TopicCreate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.create_topic(db, topic)

@router.get("/", response_model=TopicListOut)
def get_all_topics(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_all_topics(db, skip, limit)

@router.get("/{topic_id}", response_model=TopicOut)
def get_topic_by_id(topic_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_topic_by_id(db, topic_id)

@router.get("/chapters/{chapter_id}/topics", response_model=TopicListOut)
def get_topics_by_chapter(chapter_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_topics_by_chapter(db, chapter_id, skip, limit)

@router.put("/{topic_id}", response_model=TopicOut)
def update_topic(topic_id: UUID, topic: TopicUpdate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.update_topic(db, topic_id, topic)

@router.delete("/{topic_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_topic(topic_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    crud.delete_topic(db, topic_id)
    return None

@router.get("/{topic_id}/subtopics", response_model=TopicWithSubTopicsOut)
def get_topic_with_subtopics(topic_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_topic_with_subtopics(db, topic_id) 