from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.Token import Token
from config.deps import get_current_user
from config.permission import require_type
from config.security import oauth2_scheme
from Schemas.TeacherModule.tasks import (
    SubjectCreate, SubjectOut, SubjectUpdate, SubjectListOut, SubjectWithHierarchyOut
)
from Cruds.TeacherModule import tasks as crud
from config.session import get_db

router = APIRouter()

# ===== SUBJECT ROUTES =====
@router.post("/", response_model=SubjectOut)
def create_subject(subject: SubjectCreate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.create_subject(db, subject)

@router.get("/", response_model=SubjectListOut)
def get_all_subjects(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_all_subjects(db, skip, limit)

@router.get("/{subject_id}", response_model=SubjectOut)
def get_subject_by_id(subject_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_subject_by_id(db, subject_id)

@router.put("/{subject_id}", response_model=SubjectOut)
def update_subject(subject_id: UUID, subject: SubjectUpdate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.update_subject(db, subject_id, subject)

@router.delete("/{subject_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_subject(subject_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    crud.delete_subject(db, subject_id)
    return None

@router.get("/{subject_id}/hierarchy", response_model=SubjectWithHierarchyOut)
def get_subject_with_hierarchy(subject_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_subject_with_hierarchy(db, subject_id)