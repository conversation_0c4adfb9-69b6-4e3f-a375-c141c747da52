from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from Schemas.Token import Token
from config.deps import get_current_user
from config.permission import require_type
from config.security import oauth2_scheme
from Schemas.TeacherModule.tasks import (
    SubTopicCreate, SubTopicOut, SubTopicUpdate, SubTopicListOut
)
from Cruds.TeacherModule import tasks as crud
from config.session import get_db

router = APIRouter()

# ===== SUBTOPIC ROUTES =====
@router.post("/", response_model=SubTopicOut)
def create_subtopic(subtopic: SubTopicCreate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.create_subtopic(db, subtopic)

@router.get("/", response_model=SubTopicListOut)
def get_all_subtopics(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_all_subtopics(db, skip, limit)

@router.get("/{subtopic_id}", response_model=SubTopicOut)
def get_subtopic_by_id(subtopic_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_subtopic_by_id(db, subtopic_id)

@router.get("/topics/{topic_id}/subtopics", response_model=SubTopicListOut)
def get_subtopics_by_topic(topic_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_subtopics_by_topic(db, topic_id, skip, limit)

@router.put("/{subtopic_id}", response_model=SubTopicOut)
def update_subtopic(subtopic_id: UUID, subtopic: SubTopicUpdate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.update_subtopic(db, subtopic_id, subtopic)

@router.delete("/{subtopic_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_subtopic(subtopic_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    crud.delete_subtopic(db, subtopic_id)
    return None 