from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID

from config.session import get_db
from config.security import oauth2_scheme
from config.permission import require_type
from config.deps import get_current_user
from Cruds.StudentDashboard import get_student_dashboard_data
from Schemas.StudentDashboard import StudentDashboardData

router = APIRouter()

@router.get("/dashboard", response_model=StudentDashboardData)
def get_student_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get comprehensive student dashboard data including:
    
    **Student Information:**
    - Complete student profile (name, email, profile picture, etc.)
    
    **Academic Data:**
    - Enrolled classes with teacher information
    - Current assignments and tasks with deadlines
    - Upcoming exams with schedules
    - Academic performance metrics and grades
    
    **Activity & Engagement:**
    - Recent activity feed (submissions, completions, etc.)
    - Study metrics and gamification data (points, level)
    
    **Planning & Organization:**
    - Upcoming schedule with exams and assignment deadlines
    - Quick action items (overdue tasks, upcoming exams)
    - Unread notifications count
    
    **Response Format:**
    ```json
    {
      "success": true,
      "data": {
        "student": { /* student profile */ },
        "classes": [ /* enrolled classes */ ],
        "exams": [ /* upcoming exams */ ],
        "performance": { /* grades and metrics */ },
        "assignments": [ /* current tasks */ ],
        "recent_activity": [ /* activity feed */ ],
        "study_metrics": { /* gamification data */ },
        "schedule": [ /* upcoming events */ ],
        "quick_actions": [ /* actionable items */ ],
        "unread_notifications_count": 3,
        "last_updated": "2024-01-15T15:30:00Z"
      }
    }
    ```
    
    **Authentication:** Requires student role authentication.
    """
    try:
        current_user = get_current_user(token, db)
        student_id = UUID(str(current_user.id))
        
        dashboard_data = get_student_dashboard_data(db, student_id)
        return dashboard_data
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch dashboard data: {str(e)}"
        )

@router.get("/dashboard/summary")
def get_dashboard_summary(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get a lightweight summary of key dashboard metrics for quick loading.
    
    Returns essential counts and metrics without detailed data:
    - Total classes enrolled
    - Pending assignments count
    - Upcoming exams count
    - Overall grade average
    - Unread notifications count
    """
    try:
        current_user = get_current_user(token, db)
        student_id = UUID(str(current_user.id))
        
        # Get full dashboard data
        dashboard_data = get_student_dashboard_data(db, student_id)
        
        # Extract summary metrics
        summary = {
            "success": True,
            "data": {
                "total_classes": len(dashboard_data.data.classes),
                "pending_assignments": len([
                    task for task in dashboard_data.data.assignments 
                    if task.submission_date is None
                ]),
                "upcoming_exams": len(dashboard_data.data.exams),
                "overall_grade": dashboard_data.data.performance.overall_grade,
                "unread_notifications": dashboard_data.data.unread_notifications_count,
                "total_points": dashboard_data.data.study_metrics.total_points,
                "current_level": dashboard_data.data.study_metrics.level,
                "quick_actions_count": len(dashboard_data.data.quick_actions),
                "last_updated": dashboard_data.data.last_updated
            }
        }
        
        return summary
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch dashboard summary: {str(e)}"
        )

@router.get("/dashboard/quick-actions")
def get_quick_actions(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get only the quick actions for the student dashboard.
    Useful for real-time updates of actionable items.
    """
    try:
        current_user = get_current_user(token, db)
        student_id = UUID(str(current_user.id))
        
        dashboard_data = get_student_dashboard_data(db, student_id)
        
        return {
            "success": True,
            "data": {
                "quick_actions": dashboard_data.data.quick_actions,
                "last_updated": dashboard_data.data.last_updated
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch quick actions: {str(e)}"
        )

@router.get("/dashboard/performance")
def get_performance_metrics(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get detailed performance and study metrics for analytics views.
    """
    try:
        current_user = get_current_user(token, db)
        student_id = UUID(str(current_user.id))
        
        dashboard_data = get_student_dashboard_data(db, student_id)
        
        return {
            "success": True,
            "data": {
                "performance": dashboard_data.data.performance,
                "study_metrics": dashboard_data.data.study_metrics,
                "last_updated": dashboard_data.data.last_updated
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch performance metrics: {str(e)}"
        )

@router.get("/dashboard/schedule")
def get_student_schedule(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get the student's upcoming schedule including exams and assignment deadlines.
    """
    try:
        current_user = get_current_user(token, db)
        student_id = UUID(str(current_user.id))
        
        dashboard_data = get_student_dashboard_data(db, student_id)
        
        return {
            "success": True,
            "data": {
                "schedule": dashboard_data.data.schedule,
                "last_updated": dashboard_data.data.last_updated
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Failed to fetch schedule: {str(e)}"
        )
