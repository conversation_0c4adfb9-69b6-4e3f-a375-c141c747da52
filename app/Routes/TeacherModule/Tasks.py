from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from Schemas.Token import Token
from config.deps import get_current_user
from config.permission import require_type
from config.security import oauth2_scheme
from Schemas.TeacherModule.tasks import (
    TaskCreate, TaskOut, TaskUpdate, TaskListOut, TaskWithDetailsOut, MyTasks,
    TaskStudentOut, TaskClassroomOut, TaskClassroomStudentOut, TaskCreateWithAssignments,
    TaskCreateForStudent, TaskCreateForClassroom, TaskCreateForMultipleStudents, TaskCreateForMultipleClassrooms,
    TaskAttachmentOut, StudentTaskAttachmentOut
)
from Cruds.TeacherModule import tasks as crud
from config.session import get_db

router = APIRouter()

# === POST endpoints ===
@router.post("/for-student", response_model=TaskOut)
def create_task_for_student(task: TaskCreateForStudent, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_student(db, task)

@router.post("/for-classroom", response_model=TaskOut)
def create_task_for_classroom(task: TaskCreateForClassroom, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_classroom(db, task)

@router.post("/for-multiple-students", response_model=TaskOut)
def create_task_for_multiple_students(task: TaskCreateForMultipleStudents, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_multiple_students(db, task)

@router.post("/for-multiple-classrooms", response_model=TaskOut)
def create_task_for_multiple_classrooms(task: TaskCreateForMultipleClassrooms, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_multiple_classrooms(db, task)

@router.post("/{task_id}/students/bulk", response_model=List[TaskStudentOut])
def assign_task_to_multiple_students(task_id: UUID, student_ids: List[UUID], db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.assign_task_to_multiple_students(db, task_id, student_ids)

# === GET endpoints ===
@router.get("/{task_id}", response_model=TaskOut)
def get_task_by_id(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_task_by_id(db, task_id)

@router.get("/{task_id}/details", response_model=TaskWithDetailsOut)
def get_task_with_details(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    """Get task details including all attachments and assignments"""
    return crud.get_task_with_details(db, task_id)

@router.get("/", response_model=TaskListOut)
def get_all_tasks_with_filters(
    subject_id: Optional[UUID] = None,
    chapter_id: Optional[UUID] = None,
    topic_id: Optional[UUID] = None,
    subtopic_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme)
):
    """
    Filter tasks by optional subject_id, chapter_id, topic_id, subtopic_id.
    Returns tasks with attachments included.
    """
    query = db.query(crud.Task)
    if subject_id is not None:
        query = query.filter(crud.Task.subject_id == subject_id)
    if chapter_id is not None:
        query = query.join(crud.TaskChapter).filter(crud.TaskChapter.chapter_id == chapter_id)
    if topic_id is not None:
        query = query.join(crud.TaskTopic).filter(crud.TaskTopic.topic_id == topic_id)
    if subtopic_id is not None:
        query = query.join(crud.TaskSubTopic).filter(crud.TaskSubTopic.subtopic_id == subtopic_id)
    total = query.count()
    tasks = query.offset(skip).limit(limit).all()

    # Get attachments for all tasks
    task_ids = [task.id for task in tasks]
    attachments = db.query(crud.TaskAttachment).filter(crud.TaskAttachment.task_id.in_(task_ids)).all()

    # Group attachments by task_id
    attachments_by_task = {}
    for attachment in attachments:
        if attachment.task_id not in attachments_by_task:
            attachments_by_task[attachment.task_id] = []
        attachments_by_task[attachment.task_id].append(attachment)

    # Build task objects with attachments
    task_objects = []
    for task in tasks:
        task_data = crud.TaskOut.model_validate(task)
        task_dict = task_data.dict()
        task_dict['attachments'] = [
            TaskAttachmentOut.model_validate(att)
            for att in attachments_by_task.get(task.id, [])
        ]
        task_objects.append(crud.TaskOut(**task_dict))

    return TaskListOut(tasks=task_objects, total=total)

@router.get("/classrooms/{classroom_id}", response_model=TaskListOut)
def get_tasks_by_classroom(classroom_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_tasks_by_classroom(db, classroom_id, skip, limit)

@router.get("/my/tasks", response_model=MyTasks)
def get_tasks_by_student(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("student"))):
    current_user = get_current_user(token, db)
    return crud.get_tasks_by_student(db, student_id=UUID(str(current_user.id)), skip=skip, limit=limit)

@router.get("/{task_id}/students", response_model=List[TaskStudentOut])
def get_students_by_task(task_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_students_by_task(db, task_id, skip, limit)

@router.get("/{task_id}/attachments", response_model=List[TaskAttachmentOut])
def get_task_attachments(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    """Get all teacher attachments for a specific task"""
    return crud.get_task_attachments(db, task_id)

@router.get("/{task_id}/student-attachments", response_model=List[StudentTaskAttachmentOut])
def get_all_student_attachments_for_task(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    """Get all student attachments for a specific task (teacher only)"""
    return crud.get_all_student_attachments_for_task(db, task_id)

@router.get("/{task_id}/my-attachments", response_model=List[StudentTaskAttachmentOut])
def get_my_task_attachments(task_id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))):
    """Get student's own attachments for a specific task"""
    current_user = get_current_user(token, db)
    return crud.get_student_task_attachments(db, task_id, UUID(str(current_user.id)))



# === PUT endpoints ===
@router.put("/{task_id}", response_model=TaskOut)
def update_task(task_id: UUID, task: TaskUpdate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.update_task(db, task_id, task)

# === DELETE endpoints ===
@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_task(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    crud.delete_task(db, task_id)
    return None

