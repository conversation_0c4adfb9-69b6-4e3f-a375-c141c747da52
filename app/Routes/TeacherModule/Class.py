from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.TeacherModule.Class import ClassNumberCreate, ClassNumberUpdate, ClassNumberOut
from Cruds.TeacherModule.Class import (
    create_class_number,
    get_all_class_numbers,
    get_class_number_by_id,
    update_class_number,
    delete_class_number
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme
from config.permission import require_type

router = APIRouter()

@router.get("/", response_model=List[ClassNumberOut])
def read_class_numbers(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    return get_all_class_numbers(db)

@router.get("/{class_id}", response_model=ClassNumberOut)
def read_class_number(class_id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    return get_class_number_by_id(db, class_id)

@router.post("/", response_model=ClassNumberOut)
def create_class_number_endpoint(class_in: ClassNumberCreate, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return create_class_number(db, class_in)

@router.put("/{class_id}", response_model=ClassNumberOut)
def update_class_number_endpoint(class_id: UUID, class_update: ClassNumberUpdate, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return update_class_number(db, class_id, class_update)

@router.delete("/{class_id}", status_code=204)
def delete_class_number_endpoint(class_id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    delete_class_number(db, class_id)
    return None 