"""
File Management Service

This module provides utilities for managing uploaded files including
cleanup, maintenance, and administrative operations.
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from config.config import settings
from config.logging import get_logger
from Models.users import User
from Models.Tasks import TaskAttachment, StudentTaskAttachment
from services.file_storage import file_storage

logger = get_logger(__name__)

class FileManagementService:
    """Service for file management and cleanup operations"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
    
    def get_storage_stats(self) -> Dict:
        """Get storage usage statistics"""
        try:
            stats = {
                "total_files": 0,
                "total_size_bytes": 0,
                "profile_pictures": {"count": 0, "size_bytes": 0},
                "task_attachments": {"count": 0, "size_bytes": 0},
                "orphaned_files": {"count": 0, "size_bytes": 0}
            }
            
            if not self.upload_dir.exists():
                return stats
            
            # Walk through all files
            for file_path in self.upload_dir.rglob("*"):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    stats["total_files"] += 1
                    stats["total_size_bytes"] += file_size
                    
                    # Categorize by directory
                    relative_path = file_path.relative_to(self.upload_dir)
                    if str(relative_path).startswith("profile_pictures"):
                        stats["profile_pictures"]["count"] += 1
                        stats["profile_pictures"]["size_bytes"] += file_size
                    elif str(relative_path).startswith("task_attachments"):
                        stats["task_attachments"]["count"] += 1
                        stats["task_attachments"]["size_bytes"] += file_size
            
            # Convert bytes to human readable format
            stats["total_size_mb"] = round(stats["total_size_bytes"] / (1024 * 1024), 2)
            stats["profile_pictures"]["size_mb"] = round(
                stats["profile_pictures"]["size_bytes"] / (1024 * 1024), 2
            )
            stats["task_attachments"]["size_mb"] = round(
                stats["task_attachments"]["size_bytes"] / (1024 * 1024), 2
            )
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {str(e)}")
            return {"error": str(e)}
    
    def find_orphaned_files(self, db: Session) -> List[Dict]:
        """Find files that exist on disk but not in database"""
        orphaned_files = []
        
        try:
            if not self.upload_dir.exists():
                return orphaned_files
            
            # Get all file paths from database
            db_profile_pictures = set()
            db_task_attachments = set()
            
            # Profile pictures from users
            users_with_pics = db.query(User).filter(User.profile_picture.isnot(None)).all()
            for user in users_with_pics:
                if user.profile_picture:
                    db_profile_pictures.add(user.profile_picture)
                    # Also add thumbnail path
                    thumbnail_path = user.profile_picture.replace(
                        "profile_pictures/", "profile_pictures/thumbnails/"
                    )
                    db_profile_pictures.add(thumbnail_path)
            
            # Task attachments
            task_attachments = db.query(TaskAttachment).all()
            for attachment in task_attachments:
                db_task_attachments.add(attachment.file_url)
            
            # Student task attachments
            student_attachments = db.query(StudentTaskAttachment).all()
            for attachment in student_attachments:
                db_task_attachments.add(attachment.file_url)
            
            # Check all files on disk
            for file_path in self.upload_dir.rglob("*"):
                if file_path.is_file():
                    relative_path = str(file_path.relative_to(self.upload_dir))
                    
                    # Check if file is referenced in database
                    is_referenced = (
                        relative_path in db_profile_pictures or
                        relative_path in db_task_attachments
                    )
                    
                    if not is_referenced:
                        file_stats = file_path.stat()
                        orphaned_files.append({
                            "path": relative_path,
                            "full_path": str(file_path),
                            "size_bytes": file_stats.st_size,
                            "size_mb": round(file_stats.st_size / (1024 * 1024), 2),
                            "created": datetime.fromtimestamp(file_stats.st_ctime),
                            "modified": datetime.fromtimestamp(file_stats.st_mtime)
                        })
            
            logger.info(f"Found {len(orphaned_files)} orphaned files")
            return orphaned_files
            
        except Exception as e:
            logger.error(f"Failed to find orphaned files: {str(e)}")
            return []
    
    def cleanup_orphaned_files(self, db: Session, dry_run: bool = True) -> Dict:
        """Clean up orphaned files"""
        orphaned_files = self.find_orphaned_files(db)
        
        result = {
            "found": len(orphaned_files),
            "deleted": 0,
            "failed": 0,
            "total_size_freed_mb": 0,
            "dry_run": dry_run,
            "errors": []
        }
        
        if not orphaned_files:
            return result
        
        for file_info in orphaned_files:
            try:
                if not dry_run:
                    file_path = Path(file_info["full_path"])
                    if file_path.exists():
                        file_path.unlink()
                        result["deleted"] += 1
                        result["total_size_freed_mb"] += file_info["size_mb"]
                        logger.info(f"Deleted orphaned file: {file_info['path']}")
                else:
                    result["deleted"] += 1
                    result["total_size_freed_mb"] += file_info["size_mb"]
                    
            except Exception as e:
                result["failed"] += 1
                result["errors"].append(f"Failed to delete {file_info['path']}: {str(e)}")
                logger.error(f"Failed to delete orphaned file {file_info['path']}: {str(e)}")
        
        if dry_run:
            logger.info(f"DRY RUN: Would delete {result['deleted']} orphaned files, freeing {result['total_size_freed_mb']:.2f} MB")
        else:
            logger.info(f"Deleted {result['deleted']} orphaned files, freed {result['total_size_freed_mb']:.2f} MB")
        
        return result
    
    def cleanup_old_files(self, days_old: int = 30, dry_run: bool = True) -> Dict:
        """Clean up files older than specified days that are not referenced in database"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        result = {
            "found": 0,
            "deleted": 0,
            "failed": 0,
            "total_size_freed_mb": 0,
            "cutoff_date": cutoff_date,
            "dry_run": dry_run,
            "errors": []
        }
        
        try:
            if not self.upload_dir.exists():
                return result
            
            for file_path in self.upload_dir.rglob("*"):
                if file_path.is_file():
                    file_stats = file_path.stat()
                    file_modified = datetime.fromtimestamp(file_stats.st_mtime)
                    
                    if file_modified < cutoff_date:
                        result["found"] += 1
                        file_size_mb = file_stats.st_size / (1024 * 1024)
                        
                        try:
                            if not dry_run:
                                file_path.unlink()
                                logger.info(f"Deleted old file: {file_path}")
                            
                            result["deleted"] += 1
                            result["total_size_freed_mb"] += file_size_mb
                            
                        except Exception as e:
                            result["failed"] += 1
                            result["errors"].append(f"Failed to delete {file_path}: {str(e)}")
            
            if dry_run:
                logger.info(f"DRY RUN: Would delete {result['deleted']} old files, freeing {result['total_size_freed_mb']:.2f} MB")
            else:
                logger.info(f"Deleted {result['deleted']} old files, freed {result['total_size_freed_mb']:.2f} MB")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to cleanup old files: {str(e)}")
            result["errors"].append(str(e))
            return result
    
    def validate_file_integrity(self, db: Session) -> Dict:
        """Validate that all database file references point to existing files"""
        result = {
            "total_checked": 0,
            "missing_files": [],
            "invalid_references": 0
        }
        
        try:
            # Check profile pictures
            users_with_pics = db.query(User).filter(User.profile_picture.isnot(None)).all()
            for user in users_with_pics:
                result["total_checked"] += 1
                if user.profile_picture:
                    full_path = self.upload_dir / user.profile_picture
                    if not full_path.exists():
                        result["missing_files"].append({
                            "type": "profile_picture",
                            "user_id": str(user.id),
                            "path": user.profile_picture
                        })
                        result["invalid_references"] += 1
            
            # Check task attachments
            task_attachments = db.query(TaskAttachment).all()
            for attachment in task_attachments:
                result["total_checked"] += 1
                full_path = self.upload_dir / attachment.file_url
                if not full_path.exists():
                    result["missing_files"].append({
                        "type": "task_attachment",
                        "attachment_id": str(attachment.id),
                        "task_id": str(attachment.task_id),
                        "path": attachment.file_url
                    })
                    result["invalid_references"] += 1
            
            # Check student task attachments
            student_attachments = db.query(StudentTaskAttachment).all()
            for attachment in student_attachments:
                result["total_checked"] += 1
                full_path = self.upload_dir / attachment.file_url
                if not full_path.exists():
                    result["missing_files"].append({
                        "type": "student_task_attachment",
                        "attachment_id": str(attachment.id),
                        "task_id": str(attachment.task_id),
                        "student_id": str(attachment.student_id),
                        "path": attachment.file_url
                    })
                    result["invalid_references"] += 1
            
            logger.info(f"File integrity check: {result['invalid_references']} missing files out of {result['total_checked']} checked")
            return result
            
        except Exception as e:
            logger.error(f"Failed to validate file integrity: {str(e)}")
            return {"error": str(e)}
    
    def create_backup(self, backup_dir: str) -> Dict:
        """Create a backup of all uploaded files"""
        backup_path = Path(backup_dir)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"edufair_files_backup_{timestamp}"
        full_backup_path = backup_path / backup_name
        
        result = {
            "backup_path": str(full_backup_path),
            "success": False,
            "files_backed_up": 0,
            "total_size_mb": 0
        }
        
        try:
            # Create backup directory
            backup_path.mkdir(parents=True, exist_ok=True)
            
            if self.upload_dir.exists():
                # Copy entire upload directory
                shutil.copytree(self.upload_dir, full_backup_path)
                
                # Calculate stats
                for file_path in full_backup_path.rglob("*"):
                    if file_path.is_file():
                        result["files_backed_up"] += 1
                        result["total_size_mb"] += file_path.stat().st_size / (1024 * 1024)
                
                result["success"] = True
                result["total_size_mb"] = round(result["total_size_mb"], 2)
                
                logger.info(f"Backup created: {full_backup_path} ({result['files_backed_up']} files, {result['total_size_mb']} MB)")
            else:
                logger.warning("Upload directory does not exist, creating empty backup")
                full_backup_path.mkdir(parents=True, exist_ok=True)
                result["success"] = True
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to create backup: {str(e)}")
            result["error"] = str(e)
            return result

# Global instance
file_manager = FileManagementService()
