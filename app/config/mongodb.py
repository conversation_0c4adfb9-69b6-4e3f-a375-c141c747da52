# from pymongo import MongoClient
# from pymongo.database import Database
# from pymongo.collection import Collection
# from .config import settings
import logging
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient
import asyncio
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB configuration
MONGODB_USERNAME = os.getenv("MONGODB_USERNAME")
MONGODB_PASSWORD = os.getenv("MONGODB_PASSWORD")
MONGODB_HOST = "*************"
MONGODB_PORT = 27017

MONGODB_URL = f"mongodb://{MONGODB_USERNAME}:{MONGODB_PASSWORD}@{MONGODB_HOST}:{MONGODB_PORT}"
MONGODB_DB = "AI"

_mongo_client: Optional[AsyncIOMotorClient] = None
_mongo_db = None

async def init_mongodb():
    """Initialize MongoDB connection on app startup"""
    global _mongo_client, _mongo_db
    _mongo_client = AsyncIOMotorClient(MONGODB_URL)
    _mongo_db = _mongo_client[MONGODB_DB]
    return _mongo_db

async def close_mongodb():
    """Close MongoDB connection on app shutdown"""
    global _mongo_client
    if _mongo_client:
        _mongo_client.close()
        _mongo_client = None

async def get_mongodb():
    """Dependency to get MongoDB database instance"""
    if _mongo_db is None:
        raise RuntimeError("MongoDB not initialized. Call init_mongodb() first.")
    return _mongo_db

def get_notifications_collection():
    """Get notifications collection from MongoDB"""
    if _mongo_db is None:
        raise RuntimeError("MongoDB not initialized.")
    return _mongo_db.notifications

def get_logs_collection():
    """Stub: Get the logs collection"""
    raise NotImplementedError("MongoDB connection is temporarily disabled.")

def get_audit_collection():
    """Stub: Get the audit collection"""
    raise NotImplementedError("MongoDB connection is temporarily disabled.") 