# Disqualified Student Handling - API Documentation

This document explains how the exam system handles disqualified students across AI checking, basic results, and all exam-related APIs.

## Overview

When a student is disqualified from an exam (due to cheating, violations, etc.), the system properly detects and communicates this status across all exam-related APIs. The disqualification status is stored in the `StudentExamAttempt.status` field as "disqualified".

## AI Checking API Response for Disqualified Students

### Endpoint: POST `/api/exams/checking/ai-check/{exam_id}`
### Endpoint: GET `/api/exams/checking/ai-results/{exam_id}`

When a disqualified student attempts to use AI checking or retrieve AI results, the system returns:

```json
{
  "success": false,
  "message": "Cannot check exam - student was disqualified from this exam",
  "is_disqualified": true,
  "disqualification_reason": "Student was disqualified during the exam session"
}
```

### For AI Results Retrieval (when no AI results exist but student is disqualified):

```json
{
  "success": true,
  "message": "Student was disqualified from this exam and has failed",
  "is_disqualified": true,
  "disqualification_reason": "Student was disqualified during the exam session",
  "result": {
    "attempt_status": "disqualified",
    "total_score": 0,
    "max_total_score": 0,
    "percentage": 0.0,
    "question_feedbacks": []
  }
}
```

## Basic Exam Results API Response for Disqualified Students

### Endpoint: GET `/api/exams/checking/results/{exam_id}` (Students)
### Endpoint: GET `/api/exams/checking/teacher/results/{exam_id}/{student_id}` (Teachers)

When retrieving basic exam results for a disqualified student, the system returns:

```json
{
  "success": true,
  "message": "Student was disqualified from this exam and has failed",
  "is_disqualified": true,
  "disqualification_reason": "Student was disqualified during the exam session",
  "result": {
    "exam_id": "123e4567-e89b-12d3-a456-426614174000",
    "student_id": "987fcdeb-51d2-4567-8901-123456789abc",
    "attempt_id": "456789ab-cdef-1234-5678-901234567890",
    "exam_title": "Mathematics Final Exam",
    "student_name": "John Doe",
    "is_disqualified": true,
    "disqualification_reason": "Student was disqualified during the exam session",
    "attempt_status": "disqualified",
    "total_score": 0,
    "max_total_score": 0,
    "percentage": 0.0,
    "has_teacher_results": false,
    "has_ai_results": false,
    "submitted_at": "2024-01-15T14:30:00Z"
  }
}
```

## Basic Results API Features

The new basic results endpoints provide:

1. **Optimized Database Queries**: Uses database-level joins to minimize queries
2. **Teacher Results Priority**: Shows teacher results if available, otherwise AI results
3. **Disqualification Handling**: Properly detects and handles disqualified students
4. **Performance Optimized**: Single query to get attempt, exam, and student info
5. **Comprehensive Response**: Includes all necessary information for frontend display

### For Non-Disqualified Students:

```json
{
  "success": true,
  "is_disqualified": false,
  "result": {
    "exam_id": "123e4567-e89b-12d3-a456-426614174000",
    "student_id": "987fcdeb-51d2-4567-8901-123456789abc",
    "attempt_id": "456789ab-cdef-1234-5678-901234567890",
    "exam_title": "Mathematics Final Exam",
    "student_name": "John Doe",
    "is_disqualified": false,
    "attempt_status": "submitted",
    "total_score": 85,
    "max_total_score": 100,
    "percentage": 85.0,
    "has_teacher_results": true,
    "has_ai_results": false,
    "results_source": "teacher",
    "submitted_at": "2024-01-15T14:30:00Z"
  }
}
```

## Key Features

1. **Immediate Detection**: The system checks `StudentExamAttempt.status == "disqualified"` before any processing
2. **Consistent Response**: All APIs return standardized disqualification information
3. **Automatic Failure**: Disqualified students automatically receive 0 scores
4. **Clear Messaging**: Responses clearly indicate disqualification status and reason
5. **No Processing**: Disqualified attempts skip expensive AI checking or complex score calculations
6. **Database Optimization**: Uses optimized queries with joins to minimize database calls

## Implementation Details

- **Database Field**: `StudentExamAttempt.status` stores "disqualified" value
- **Early Return**: APIs check disqualification status before any processing
- **Standardized Fields**: All responses include `is_disqualified` and `disqualification_reason`
- **Performance Optimized**: Disqualified checks happen at database level before expensive operations
- **Query Optimization**: Basic results use single queries with joins for better performance

## Frontend Integration

Frontend applications should:

1. **Check `is_disqualified` field** in all exam-related API responses
2. **Display appropriate messaging** when students are disqualified
3. **Prevent further actions** (like retaking exams) for disqualified students
4. **Show clear failure status** with disqualification reason
5. **Use basic results endpoints** for faster loading of exam scores without AI processing

This ensures a consistent user experience and proper handling of academic integrity violations across all exam-related functionality.