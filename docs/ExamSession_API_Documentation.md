# ExamSession API Documentation

## Overview
The ExamSession API provides comprehensive exam session management including session lifecycle, real-time communication, reconnection handling, and administrative controls.

**Base URL:** `https://edufair.duckdns.org/api/exams/session`

## Authentication
All endpoints require JWT Bearer token authentication:
```
Authorization: Bearer {jwt_token}
```

## API Endpoints

### 1. Start Exam Session

**POST** `/exam-session/start`

Initiates a new exam session for a student.

#### Request Body
```json
{
  "exam_id": "string (UUID)"
}
```

#### Response
```json
{
  "session_id": "string (UUID)"
}
```

#### Status Codes
- `200` - Session started successfully
- `404` - Exam not found
- `401` - Unauthorized
- `403` - Forbidden (not a student)

#### Example
```bash
curl -X POST "https://edufair.duckdns.org/api/exams/session/exam-session/start" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{"exam_id": "8d81b7bf-6f2b-46be-8410-de931a527cdf"}'
```

---

### 2. Submit Exam Session

**POST** `/exam-session/submit`

Submits a completed exam session.

#### Request Body
```json
{
  "session_id": "string (UUID)"
}
```

#### Response
```json
{
  "success": "boolean",
  "disqualified": "boolean",
  "disqualification_reason": "string | null"
}
```

#### Status Codes
- `200` - Session submitted successfully
- `404` - Session not found or expired
- `403` - Permission denied
- `401` - Unauthorized

#### Example
```bash
curl -X POST "https://edufair.duckdns.org/api/exams/session/exam-session/submit" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{"session_id": "123e4567-e89b-12d3-a456-************"}'
```

---

### 3. Request Reconnection

**POST** `/exam-session/reconnect`

Requests permission to reconnect to a disconnected exam session.

#### Request Body
```json
{
  "session_id": "string (UUID)",
  "reason": "string"
}
```

#### Response
```json
{
  "request_id": "string (UUID)",
  "status": "string"
}
```

#### Status Codes
- `200` - Reconnection request submitted
- `404` - Session not found
- `401` - Unauthorized

#### Example
```bash
curl -X POST "https://edufair.duckdns.org/api/exams/session/exam-session/reconnect" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "123e4567-e89b-12d3-a456-************",
    "reason": "Internet connection lost"
  }'
```

---

### 4. Check Reconnection Status

**GET** `/exam-session/reconnect/{request_id}/status`

Checks the status of a reconnection request.

#### Path Parameters
- `request_id` (string, UUID) - The reconnection request ID

#### Response
```json
{
  "status": "string",
  "teacher_reason": "string | null",
  "session_id": "string | null"
}
```

#### Status Values
- `pending` - Awaiting teacher approval
- `approved` - Approved by teacher
- `denied` - Denied by teacher

#### Status Codes
- `200` - Status retrieved successfully
- `404` - Request not found
- `401` - Unauthorized

#### Example
```bash
curl -X GET "https://edufair.duckdns.org/api/exams/session/exam-session/reconnect/456e7890-e89b-12d3-a456-************/status" \
  -H "Authorization: Bearer {token}"
```

---

### 5. Resume Exam Session

**GET** `/exam-session/{session_id}/resume`

Resumes an approved exam session after reconnection.

#### Path Parameters
- `session_id` (string, UUID) - The exam session ID

#### Response
```json
{
  "session_id": "string (UUID)",
  "exam_id": "string (UUID)",
  "exam_title": "string",
  "current_answers": {
    "question_id": "answer_text"
  },
  "remaining_time_seconds": "integer",
  "total_duration_seconds": "integer",
  "strikes": "integer",
  "status": "string"
}
```

#### Status Codes
- `200` - Session data retrieved successfully
- `404` - Session not found
- `403` - Permission denied
- `401` - Unauthorized

#### Example
```bash
curl -X GET "https://edufair.duckdns.org/api/exams/session/exam-session/123e4567-e89b-12d3-a456-************/resume" \
  -H "Authorization: Bearer {token}"
```

---

## Teacher Endpoints

### 6. Get Pending Reconnection Requests

**GET** `/teacher/reconnection-requests`

Retrieves all pending reconnection requests for teacher approval.

#### Response
```json
[
  {
    "request_id": "string (UUID)",
    "session_id": "string (UUID)",
    "student_id": "string (UUID)",
    "exam_id": "string (UUID)",
    "reason": "string",
    "status": "string",
    "requested_at": "string (ISO datetime)"
  }
]
```

#### Status Codes
- `200` - Requests retrieved successfully
- `401` - Unauthorized
- `403` - Forbidden (not a teacher)

#### Example
```bash
curl -X GET "https://edufair.duckdns.org/api/exams/session/teacher/reconnection-requests" \
  -H "Authorization: Bearer {token}"
```

---

### 7. Approve/Deny Reconnection Request

**POST** `/teacher/reconnection-requests/{request_id}/approve`

Approves or denies a student's reconnection request.

#### Path Parameters
- `request_id` (string, UUID) - The reconnection request ID

#### Request Body
```json
{
  "request_id": "string (UUID)",
  "approved": "boolean",
  "reason": "string | null"
}
```

#### Response
```json
{
  "status": "string",
  "session_id": "string | null"
}
```

#### Status Codes
- `200` - Request processed successfully
- `404` - Request not found
- `401` - Unauthorized
- `403` - Forbidden (not a teacher)

#### Example
```bash
curl -X POST "https://edufair.duckdns.org/api/exams/session/teacher/reconnection-requests/456e7890-e89b-12d3-a456-************/approve" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "request_id": "456e7890-e89b-12d3-a456-************",
    "approved": true,
    "reason": "Valid technical issue"
  }'
```

---

## Admin Endpoints

### 8. View Session Details

**GET** `/admin/exam-session/{session_id}`

Retrieves detailed information about a specific exam session.

#### Path Parameters
- `session_id` (string, UUID) - The exam session ID

#### Response
```json
{
  "session_id": "string",
  "student_id": "string",
  "exam_id": "string",
  "answers": "string (JSON)",
  "strikes": "string",
  "last_heartbeat": "string (ISO datetime)",
  "status": "string",
  "start_time": "string (ISO datetime)",
  "duration": "string (seconds)"
}
```

#### Status Codes
- `200` - Session details retrieved successfully
- `404` - Session not found
- `401` - Unauthorized
- `403` - Forbidden (not an admin)

#### Example
```bash
curl -X GET "https://edufair.duckdns.org/api/exams/session/admin/exam-session/123e4567-e89b-12d3-a456-************" \
  -H "Authorization: Bearer {token}"
```

---

### 9. List Active Sessions

**GET** `/admin/exam-sessions`

Retrieves all currently active exam sessions.

#### Response
```json
[
  {
    "session_id": "string",
    "student_id": "string",
    "exam_id": "string",
    "answers": "string (JSON)",
    "strikes": "string",
    "last_heartbeat": "string (ISO datetime)",
    "status": "string",
    "start_time": "string (ISO datetime)",
    "duration": "string (seconds)"
  }
]
```

#### Status Codes
- `200` - Sessions retrieved successfully
- `401` - Unauthorized
- `403` - Forbidden (not an admin)

#### Example
```bash
curl -X GET "https://edufair.duckdns.org/api/exams/session/admin/exam-sessions" \
  -H "Authorization: Bearer {token}"
```

---

### 10. Terminate Session

**POST** `/admin/exam-session/{session_id}/terminate`

Forcefully terminates an active exam session.

#### Path Parameters
- `session_id` (string, UUID) - The exam session ID

#### Query Parameters
- `reason` (string, required) - Reason for termination

#### Response
```json
{
  "message": "string",
  "session_id": "string"
}
```

#### Status Codes
- `200` - Session terminated successfully
- `404` - Session not found
- `401` - Unauthorized
- `403` - Forbidden (not an admin)

#### Example
```bash
curl -X POST "https://edufair.duckdns.org/api/exams/session/admin/exam-session/123e4567-e89b-12d3-a456-************/terminate?reason=Suspicious%20activity" \
  -H "Authorization: Bearer {token}"
```

---

## WebSocket Endpoints

### 1. Session-Based WebSocket

**WebSocket** `/ws/exam-session/{session_id}`

Real-time communication for session-based exam management.

#### Connection URL
```
wss://edufair.duckdns.org/api/exams/session/ws/exam-session/{session_id}
```

#### Authentication
WebSocket connections do not use JWT dependency injection. Authentication should be handled via:
- Query parameters in the connection URL
- Message-based authentication after connection
- Session validation via Redis

#### Message Types

##### Client → Server
```json
{
  "type": "heartbeat"
}
```

```json
{
  "type": "cheat",
  "details": "string"
}
```

```json
{
  "answers": {
    "question_id": "answer_text"
  }
}
```

##### Server → Client
```json
{
  "type": "session_resume",
  "current_answers": {},
  "remaining_time_seconds": 3600,
  "strikes": 0,
  "session_id": "string"
}
```

```json
{
  "type": "answers_saved",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "event": "disqualified",
  "reason": "Cheating detected."
}
```

---

### 2. Direct Exam WebSocket

**WebSocket** `/ws/exam/{exam_id}/{student_id}`

Direct real-time communication for exam sessions.

#### Connection URL
```
wss://edufair.duckdns.org/ws/exam/{exam_id}/{student_id}
```

#### Authentication
WebSocket connections do not require JWT headers. Authentication is handled via:
- Path parameters (exam_id, student_id)
- Message-based validation
- Session state verification

#### Message Types

##### Client → Server
```json
{
  "type": "heartbeat",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "join_exam",
  "exam_id": "string",
  "student_id": "string",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "save_answer",
  "question_id": "string",
  "answer": "string",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "submit_exam",
  "exam_id": "string",
  "student_id": "string",
  "final_answers": [
    {
      "question_id": "string",
      "answer": "string",
      "timestamp": "2024-01-15T14:00:00Z"
    }
  ],
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "suspicious_activity",
  "activity": "tab_switch|dev_tools|copy_paste",
  "details": "string",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

##### Server → Client
```json
{
  "type": "connection_established",
  "exam_id": "string",
  "student_id": "string",
  "message": "Connected to exam session",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "heartbeat_response",
  "status": "connected",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "exam_joined",
  "exam_id": "string",
  "student_id": "string",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "answer_saved",
  "question_id": "string",
  "status": "success",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "exam_submitted",
  "exam_id": "string",
  "student_id": "string",
  "status": "success",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "activity_logged",
  "activity": "string",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "error",
  "error_code": "UNKNOWN_MESSAGE_TYPE|INVALID_JSON",
  "message": "string",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

```json
{
  "type": "timeout_warning",
  "message": "No activity detected. Please ensure connection is stable.",
  "timestamp": "2024-01-15T14:00:00Z"
}
```

---

## Error Responses

### Standard Error Format
```json
{
  "detail": "string"
}
```

### Common Error Codes
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

### WebSocket Error Codes
- `1008` - Policy Violation (authentication/authorization failed)
- `1011` - Internal Error

---

## Data Models

### StartSessionRequest
```json
{
  "exam_id": "string (UUID)"
}
```

### StartSessionResponse
```json
{
  "session_id": "string (UUID)"
}
```

### SubmitSessionRequest
```json
{
  "session_id": "string (UUID)"
}
```

### SubmitSessionResponse
```json
{
  "success": "boolean",
  "disqualified": "boolean",
  "disqualification_reason": "string | null"
}
```

### ReconnectionRequest
```json
{
  "session_id": "string (UUID)",
  "reason": "string"
}
```

### ReconnectionRequestResponse
```json
{
  "request_id": "string (UUID)",
  "status": "string"
}
```

### TeacherApprovalRequest
```json
{
  "request_id": "string (UUID)",
  "approved": "boolean",
  "reason": "string | null"
}
```

### TeacherApprovalResponse
```json
{
  "status": "string",
  "session_id": "string | null"
}
```

### ReconnectionStatusResponse
```json
{
  "status": "string",
  "teacher_reason": "string | null",
  "session_id": "string | null"
}
```

### SessionResumeResponse
```json
{
  "session_id": "string (UUID)",
  "exam_id": "string (UUID)",
  "exam_title": "string",
  "current_answers": {
    "question_id": "answer_text"
  },
  "remaining_time_seconds": "integer",
  "total_duration_seconds": "integer",
  "strikes": "integer",
  "status": "string"
}
```

### PendingReconnectionRequest
```json
{
  "request_id": "string (UUID)",
  "session_id": "string (UUID)",
  "student_id": "string (UUID)",
  "exam_id": "string (UUID)",
  "reason": "string",
  "status": "string",
  "requested_at": "string (ISO datetime)"
}
```

---

## Rate Limits
- WebSocket heartbeat: Maximum 1 per 10 seconds
- API requests: 100 requests per minute per user
- Session operations: 10 requests per minute per session

## Security Features
- JWT authentication required for all endpoints
- Role-based access control (student/teacher/admin)
- Session ownership validation
- Anti-cheating monitoring via WebSocket
- Automatic session expiration
- Admin audit logging

## Notes
- All timestamps are in ISO 8601 format (UTC)
- Session data is stored in Redis with automatic expiration
- Audit logs are stored in MongoDB
- WebSocket connections require active session validation
- Reconnection requests expire after 30 minutes if not processed
