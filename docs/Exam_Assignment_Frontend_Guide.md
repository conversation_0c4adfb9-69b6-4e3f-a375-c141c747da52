# Exam Assignment System - Frontend Developer Guide

## Overview
This guide covers the new exam assignment system that allows teachers to assign existing exams to students and classrooms after exam creation. The system provides flexible assignment options with add, remove, and replace operations.

## Authentication
All exam assignment endpoints require teacher authentication:
```javascript
headers: {
  'Authorization': `Bearer ${teacherToken}`,
  'Content-Type': 'application/json'
}
```

## API Endpoints

### 1. Assign Exam to Students/Classrooms
**Endpoint:** `POST /api/exams/{exam_id}/assign`

**Purpose:** Assign an existing exam to individual students and/or entire classrooms.

**Request Body:**
```typescript
interface ExamAssignmentRequest {
  student_ids?: string[];     // Optional: Array of student UUIDs
  classroom_ids?: string[];   // Optional: Array of classroom UUIDs  
  action: 'add' | 'remove' | 'replace';  // Default: 'add'
}
```

**Response:**
```typescript
interface ExamAssignmentResponse {
  exam_id: string;
  assigned_student_ids: string[];
  total_assigned: number;
  action_performed: string;
  message: string;
}
```

**Example Usage:**
```javascript
// Assign to individual students
const assignToStudents = async (examId, studentIds) => {
  const response = await fetch(`/api/exams/${examId}/assign`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      student_ids: studentIds,
      action: 'add'
    })
  });
  return response.json();
};

// Assign to entire classroom
const assignToClassroom = async (examId, classroomId) => {
  const response = await fetch(`/api/exams/${examId}/assign`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      classroom_ids: [classroomId],
      action: 'add'
    })
  });
  return response.json();
};

// Mixed assignment (students + classrooms)
const mixedAssignment = async (examId, studentIds, classroomIds) => {
  const response = await fetch(`/api/exams/${examId}/assign`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      student_ids: studentIds,
      classroom_ids: classroomIds,
      action: 'add'
    })
  });
  return response.json();
};
```

### 2. Remove Exam Assignment
**Endpoint:** `DELETE /api/exams/{exam_id}/unassign`

**Purpose:** Remove exam assignment from specific students.

**Request Body:**
```typescript
interface ExamUnassignmentRequest {
  student_ids: string[];  // Required: Array of student UUIDs to unassign
}
```

**Example Usage:**
```javascript
const unassignStudents = async (examId, studentIds) => {
  const response = await fetch(`/api/exams/${examId}/unassign`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      student_ids: studentIds
    })
  });
  return response.json();
};
```

### 3. Get Current Assignments
**Endpoint:** `GET /api/exams/{exam_id}/assignments`

**Purpose:** Retrieve current assignment details for an exam.

**Response:**
```typescript
interface ExamAssignmentOut {
  exam_id: string;
  assigned_student_ids: string[];
}
```

**Example Usage:**
```javascript
const getExamAssignments = async (examId) => {
  const response = await fetch(`/api/exams/${examId}/assignments`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```

## Assignment Actions

### 1. Add Action (Default)
- Adds new assignments without affecting existing ones
- Students already assigned will be skipped
- Use for expanding exam reach

```javascript
// Add more students to existing assignments
{
  student_ids: ["new-student-1", "new-student-2"],
  action: "add"
}
```

### 2. Remove Action
- Removes specified students from assignments
- Other assigned students remain unaffected
- Use for selective removal

```javascript
// Remove specific students (can also use unassign endpoint)
{
  student_ids: ["student-to-remove"],
  action: "remove"
}
```

### 3. Replace Action
- Removes ALL current assignments
- Assigns exam only to newly specified students/classrooms
- Use for complete assignment overhaul

```javascript
// Replace all current assignments
{
  student_ids: ["only-this-student"],
  action: "replace"
}
```

## UI Implementation Examples

### Assignment Modal Component
```jsx
const ExamAssignmentModal = ({ examId, onClose, onSuccess }) => {
  const [selectedStudents, setSelectedStudents] = useState([]);
  const [selectedClassrooms, setSelectedClassrooms] = useState([]);
  const [action, setAction] = useState('add');
  const [loading, setLoading] = useState(false);

  const handleAssign = async () => {
    setLoading(true);
    try {
      const result = await fetch(`/api/exams/${examId}/assign`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          student_ids: selectedStudents,
          classroom_ids: selectedClassrooms,
          action: action
        })
      });
      
      const data = await result.json();
      onSuccess(data.message);
      onClose();
    } catch (error) {
      console.error('Assignment failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal">
      <h3>Assign Exam</h3>
      
      {/* Action Selection */}
      <select value={action} onChange={(e) => setAction(e.target.value)}>
        <option value="add">Add to existing assignments</option>
        <option value="replace">Replace all assignments</option>
        <option value="remove">Remove from assignments</option>
      </select>

      {/* Student Selection */}
      <StudentSelector 
        selectedStudents={selectedStudents}
        onSelectionChange={setSelectedStudents}
      />

      {/* Classroom Selection */}
      <ClassroomSelector
        selectedClassrooms={selectedClassrooms}
        onSelectionChange={setSelectedClassrooms}
      />

      <button onClick={handleAssign} disabled={loading}>
        {loading ? 'Assigning...' : 'Assign Exam'}
      </button>
    </div>
  );
};
```

### Assignment Status Display
```jsx
const ExamAssignmentStatus = ({ examId }) => {
  const [assignments, setAssignments] = useState(null);

  useEffect(() => {
    const fetchAssignments = async () => {
      const response = await fetch(`/api/exams/${examId}/assignments`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      const data = await response.json();
      setAssignments(data);
    };
    
    fetchAssignments();
  }, [examId]);

  return (
    <div className="assignment-status">
      <h4>Current Assignments</h4>
      <p>Total Students: {assignments?.assigned_student_ids.length || 0}</p>
      
      <button onClick={() => openAssignmentModal()}>
        Manage Assignments
      </button>
    </div>
  );
};
```

## Error Handling

Common error responses and how to handle them:

```javascript
const handleAssignmentError = (error, response) => {
  switch (response.status) {
    case 400:
      return "Please select at least one student or classroom";
    case 403:
      return "You don't have permission to assign this exam";
    case 404:
      return "Exam or selected students/classrooms not found";
    default:
      return "Assignment failed. Please try again.";
  }
};
```

## Best Practices

1. **Validation**: Always validate that at least one student or classroom is selected
2. **Feedback**: Provide clear success/error messages to users
3. **Loading States**: Show loading indicators during API calls
4. **Refresh**: Refresh assignment lists after successful operations
5. **Permissions**: Only show assignment options to exam owners
6. **Confirmation**: Ask for confirmation before replace operations

## Integration with Existing Exam Management

The assignment system integrates seamlessly with existing exam workflows:

1. **After Exam Creation**: Show assignment options immediately
2. **Exam List View**: Display assignment status and quick assign buttons
3. **Exam Details**: Show detailed assignment information and management options
4. **Student Management**: Allow assignment from student/classroom management pages

This system provides teachers with flexible, powerful tools to manage exam assignments efficiently while maintaining proper security and validation.
