# Exam Session - Frontend Developer Guide

## Overview
This document outlines the complete exam session workflow, API endpoints, WebSocket requirements, and real-time features for the EduFair exam system.

## Exam Session Flow

### 1. Pre-Exam Phase
```
Student Login → View Exams → Select Exam → Check Start Time → Wait/Enter
```

### 2. During Exam Phase
```
Start Exam → Load Questions → Answer Questions → Auto-Save → Submit/Time Up
```

### 3. Post-Exam Phase
```
Submit Answers → Show Results → Return to Dashboard
```

## API Endpoints

### Get Student Exams
```http
GET /api/exams/student/all
Authorization: Bearer {token}
```

**Response:**
```json
[
    {
        "id": "exam-uuid",
        "title": "Mathematics Quiz",
        "start_time": "2024-01-15T14:00:00+00:00",
        "end_time": "2024-01-15T15:30:00+00:00",
        "total_duration": 90
    }
]
```

### Get Exam Details (Start Exam)
```http
GET /api/exams/student/{exam_id}
Authorization: Bearer {token}
```

**Response:**
```json
{
    "id": "exam-uuid",
    "questions": [
        {
            "id": "question-uuid",
            "question_text": "What is 2+2?",
            "options": ["3", "4", "5", "6"],
            "question_type": "multiple_choice"
        }
    ],
    "start_time": "2024-01-15T14:00:00+00:00",
    "end_time": "2024-01-15T15:30:00+00:00"
}
```

## WebSocket Requirements

### Connection Setup
```javascript
const examSocket = new WebSocket(`wss://edufair.duckdns.org/ws/exam/${examId}/${studentId}`);
// For local development: ws://localhost:8000/ws/exam/${examId}/${studentId}

// Note: WebSocket authentication is handled via path parameters and message validation
// No JWT headers required for WebSocket connections
```

### Required WebSocket Events

#### 1. Connection Events
```javascript
// Connection established
examSocket.onopen = function(event) {
    console.log("Connected to exam session");
    // Send initial heartbeat
    sendHeartbeat();
};

// Connection closed
examSocket.onclose = function(event) {
    console.log("Disconnected from exam session");
    // Attempt reconnection
    reconnectToExam();
};

// Connection error
examSocket.onerror = function(error) {
    console.error("WebSocket error:", error);
    // Handle connection issues
};
```

#### 2. Message Handling
```javascript
examSocket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'exam_started':
            handleExamStart(data);
            break;
        case 'time_warning':
            showTimeWarning(data.remaining_time);
            break;
        case 'exam_ended':
            handleExamEnd(data);
            break;
        case 'answer_saved':
            confirmAnswerSaved(data.question_id);
            break;
        case 'heartbeat_response':
            updateConnectionStatus('connected');
            break;
    }
};
```

### Required WebSocket Messages

#### 1. Student Actions
```javascript
// Join exam session
function joinExamSession() {
    examSocket.send(JSON.stringify({
        type: 'join_exam',
        exam_id: examId,
        student_id: studentId,
        timestamp: new Date().toISOString()
    }));
}

// Save answer
function saveAnswer(questionId, answer) {
    examSocket.send(JSON.stringify({
        type: 'save_answer',
        question_id: questionId,
        answer: answer,
        timestamp: new Date().toISOString()
    }));
}

// Submit exam
function submitExam() {
    examSocket.send(JSON.stringify({
        type: 'submit_exam',
        exam_id: examId,
        student_id: studentId,
        timestamp: new Date().toISOString()
    }));
}

// Heartbeat (every 30 seconds)
function sendHeartbeat() {
    examSocket.send(JSON.stringify({
        type: 'heartbeat',
        student_id: studentId,
        timestamp: new Date().toISOString()
    }));
}
```

#### 2. Server Responses
```javascript
// Server message types to handle
const SERVER_MESSAGE_TYPES = {
    EXAM_STARTED: 'exam_started',
    TIME_WARNING: 'time_warning',      // 10, 5, 1 minute warnings
    EXAM_ENDED: 'exam_ended',
    ANSWER_SAVED: 'answer_saved',
    HEARTBEAT_RESPONSE: 'heartbeat_response',
    ERROR: 'error'
};
```

## Real-Time Features Required

### 1. Auto-Save Answers
```javascript
let autoSaveTimer;

function setupAutoSave() {
    // Auto-save every 30 seconds
    autoSaveTimer = setInterval(() => {
        saveCurrentAnswers();
    }, 30000);
}

function saveCurrentAnswers() {
    const answers = getCurrentAnswers();
    answers.forEach(answer => {
        saveAnswer(answer.question_id, answer.value);
    });
}
```

### 2. Real-Time Timer
```javascript
let examTimer;
let remainingTime;

function startExamTimer(duration) {
    remainingTime = duration * 60; // Convert minutes to seconds
    
    examTimer = setInterval(() => {
        remainingTime--;
        updateTimerDisplay(remainingTime);
        
        // Send warnings
        if (remainingTime === 600) { // 10 minutes
            showTimeWarning("10 minutes remaining");
        } else if (remainingTime === 300) { // 5 minutes
            showTimeWarning("5 minutes remaining");
        } else if (remainingTime === 60) { // 1 minute
            showTimeWarning("1 minute remaining");
        } else if (remainingTime <= 0) {
            autoSubmitExam();
        }
    }, 1000);
}
```

### 3. Connection Monitoring
```javascript
let heartbeatInterval;
let connectionStatus = 'disconnected';

function startHeartbeat() {
    heartbeatInterval = setInterval(() => {
        if (examSocket.readyState === WebSocket.OPEN) {
            sendHeartbeat();
        } else {
            reconnectToExam();
        }
    }, 30000);
}

function reconnectToExam() {
    if (connectionStatus === 'reconnecting') return;
    
    connectionStatus = 'reconnecting';
    showConnectionWarning("Reconnecting...");
    
    // Attempt to reconnect
    setTimeout(() => {
        initializeExamSession();
    }, 2000);
}
```

## Exam Session Implementation

### 1. Initialize Exam Session
```javascript
class ExamSession {
    constructor(examId, studentId) {
        this.examId = examId;
        this.studentId = studentId;
        this.socket = null;
        this.answers = new Map();
        this.isSubmitted = false;
    }
    
    async start() {
        // Get exam details
        const examData = await this.fetchExamDetails();
        
        // Check if exam can be started
        if (!this.canStartExam(examData)) {
            throw new Error("Exam cannot be started yet");
        }
        
        // Initialize WebSocket
        this.initializeWebSocket();
        
        // Setup UI
        this.setupExamUI(examData);
        
        // Start timer and auto-save
        this.startExamTimer(examData.total_duration);
        this.setupAutoSave();
    }
    
    canStartExam(examData) {
        const now = new Date();
        const startTime = new Date(examData.start_time);
        const endTime = new Date(examData.end_time);
        
        return now >= startTime && now <= endTime;
    }
}
```

### 2. Answer Management
```javascript
function handleAnswerChange(questionId, answer) {
    // Update local storage
    answers.set(questionId, {
        value: answer,
        timestamp: new Date().toISOString(),
        saved: false
    });
    
    // Save to server
    saveAnswer(questionId, answer);
    
    // Update UI
    markQuestionAnswered(questionId);
}

function getCurrentAnswers() {
    return Array.from(answers.entries()).map(([questionId, data]) => ({
        question_id: questionId,
        value: data.value,
        timestamp: data.timestamp
    }));
}
```

### 3. Exam Submission
```javascript
function submitExam() {
    if (isSubmitted) return;
    
    // Prevent multiple submissions
    isSubmitted = true;
    
    // Disable all inputs
    disableExamInputs();
    
    // Send final answers
    submitExam();
    
    // Show submission confirmation
    showSubmissionConfirmation();
    
    // Cleanup
    clearInterval(examTimer);
    clearInterval(autoSaveTimer);
    clearInterval(heartbeatInterval);
}
```

## Error Handling

### 1. Connection Errors
```javascript
function handleConnectionError(error) {
    console.error("Connection error:", error);
    
    // Show user-friendly message
    showErrorMessage("Connection lost. Attempting to reconnect...");
    
    // Save current state locally
    saveStateLocally();
    
    // Attempt reconnection
    reconnectToExam();
}
```

### 2. Auto-Submit on Disconnect
```javascript
function handleDisconnection() {
    // If exam is active and time remaining
    if (!isSubmitted && remainingTime > 0) {
        // Auto-save current answers
        saveStateLocally();
        
        // Show warning
        showDisconnectionWarning();
        
        // Attempt to submit with saved answers
        attemptOfflineSubmission();
    }
}
```

## Security Requirements

### 1. Prevent Cheating
```javascript
// Disable right-click
document.addEventListener('contextmenu', e => e.preventDefault());

// Disable F12, Ctrl+Shift+I, etc.
document.addEventListener('keydown', function(e) {
    if (e.key === 'F12' || 
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C')) {
        e.preventDefault();
        logSuspiciousActivity('Developer tools attempt');
    }
});

// Detect tab switching
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        logSuspiciousActivity('Tab switched');
        showWarning("Please stay on the exam page");
    }
});
```

### 2. Activity Logging
```javascript
function logSuspiciousActivity(activity) {
    examSocket.send(JSON.stringify({
        type: 'suspicious_activity',
        activity: activity,
        timestamp: new Date().toISOString()
    }));
}
```

## UI Requirements

### 1. Timer Display
- Show remaining time prominently
- Change color as time runs out (green → yellow → red)
- Show warnings at 10, 5, 1 minute marks

### 2. Question Navigation
- Question palette showing answered/unanswered status
- Easy navigation between questions
- Mark for review functionality

### 3. Connection Status
- Show connection status indicator
- Display reconnection attempts
- Offline mode indicator

### 4. Auto-Save Indicator
- Show when answers are being saved
- Confirm when save is successful
- Warning if save fails

This comprehensive guide covers all aspects of implementing a robust exam session system with real-time features and proper error handling.

## ✅ WebSocket Status: LIVE

The WebSocket endpoint is now implemented in the ExamSession router and ready for use:
- **Endpoint:** `wss://edufair.duckdns.org/ws/exam/{exam_id}/{student_id}`
- **Router:** ExamSession (unified with existing session management)
- **Status:** Active and responding
- **Features:** Heartbeat, answer saving, exam submission, activity monitoring

### Quick Test
```javascript
const socket = new WebSocket(`wss://edufair.duckdns.org/ws/exam/${examId}/${studentId}`);
socket.onopen = () => console.log("Connected!");
socket.onmessage = (event) => console.log("Message:", JSON.parse(event.data));
```

The exam session system is ready for real-time implementation! 🚀
