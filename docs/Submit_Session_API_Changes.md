# Submit Session API Changes

## POST /exam-session/submit

### New Request Body:
```json
{
  "session_id": "string",
  "questions": [
    {
      "question_id": "uuid",
      "question_text": "What is 2+2?",
      "question_type": "mcq",
      "options": {
        "A": "3",
        "B": "4", 
        "C": "5",
        "D": "6"
      },
      "correct_answer": "B",
      "marks": 5
    }
  ],
  "student_answers": [
    {
      "question_id": "uuid",
      "answer": "B",
      "time_spent_seconds": 30
    }
  ]
}
```

### Question Types:
- `mcq` - Multiple choice
- `short_answer` - Short text
- `long_answer` - Long text
### Required Fields:
- `session_id` - Session UUID
- `questions` - Array of question objects
- `student_answers` - Array of answer objects

### Optional Fields:
- `options` - Only for MCQs
- `marks` - Question marks
- `time_spent_seconds` - Time per question

### Backward Compatibility:
Old format still works if questions/student_answers not provided.
