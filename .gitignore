# Virtual Environment
/.venv
/venv/
/env/
.venv/

server.log
# Environment Variables
.env
.env.local
.env.development
.env.test
.env.production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Database
*.db
*.sqlite
*.sqlite3
database.db
*.db-journal

# Logs
*.log
server.log
uvicorn.pid
logs/
log/

# Temporary files
temp.jsx
*.tmp
*.temp
.tmp/
.temp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
start.sh
uploads/
media/
static/uploads/
static/media/

# Backup files
*.bak
*.backup
*.old

# Configuration files with sensitive data
config.ini
secrets.json
credentials.json


server.log
uvicorn.pid
logs/
log/
# Miscellaneous
*.orig
*.rej
*.sublime-workspace
*.sublime-project   
# Coverage
.coverage
.coverage.* 


/__pycache__/
app/server.log
